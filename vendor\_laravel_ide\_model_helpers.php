<?php

namespace App\Models {

    /**
     * App\Models\Booking
     *
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property string|null $cancellation_reason
     * @property string|null $special_instructions
     * @property mixed $estimated_duration_minutes
     * @property decimal:2|null $distance_km
     * @property mixed $status
     * @property \Illuminate\Support\Carbon|null $delivered_at
     * @property \Illuminate\Support\Carbon|null $actual_pickup_time
     * @property \Illuminate\Support\Carbon|null $scheduled_pickup_time
     * @property mixed $pickup_time_preference
     * @property mixed $payment_status
     * @property mixed $payment_method
     * @property decimal:2|null $final_cost
     * @property decimal:2 $estimated_cost
     * @property string|null $package_description
     * @property decimal:2|null $package_weight
     * @property mixed $package_type
     * @property string $receiver_phone
     * @property string $receiver_name
     * @property decimal:8|null $delivery_longitude
     * @property decimal:8|null $delivery_latitude
     * @property string $delivery_address
     * @property string $pickup_person_phone
     * @property string $pickup_person_name
     * @property decimal:8|null $pickup_longitude
     * @property decimal:8|null $pickup_latitude
     * @property string $pickup_address
     * @property string $booking_id
     * @property mixed $origin_branch_id
     * @property mixed $user_id
     * @property int $id
     * @property-read mixed $status_color
     * @property-read mixed $formatted_status
     * @property-read \App\Models\User $customer
     * @property-read \App\Models\Branch $originBranch
     * @property-read \App\Models\Payment $payment
     * @property-read \App\Models\Review $review
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking whereUserId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking whereOriginBranchId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking whereBookingId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking wherePickupAddress($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking wherePickupLatitude($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking wherePickupLongitude($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking wherePickupPersonName($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking wherePickupPersonPhone($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking whereDeliveryAddress($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking whereDeliveryLatitude($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking whereDeliveryLongitude($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking whereReceiverName($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking whereReceiverPhone($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking wherePackageType($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking wherePackageWeight($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking wherePackageDescription($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking whereEstimatedCost($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking whereFinalCost($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking wherePaymentMethod($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking wherePaymentStatus($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking wherePickupTimePreference($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking whereScheduledPickupTime($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking whereActualPickupTime($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking whereDeliveredAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking whereStatus($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking whereDistanceKm($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking whereEstimatedDurationMinutes($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking whereSpecialInstructions($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking whereCancellationReason($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<Booking>|Booking query()
     * @method static mixed select($columns)
     * @method static mixed selectSub($query, $as)
     * @method static mixed selectRaw($expression, array $bindings)
     * @method static mixed fromSub($query, $as)
     * @method static mixed fromRaw($expression, $bindings)
     * @method static mixed createSub($query)
     * @method static mixed parseSub($query)
     * @method static mixed prependDatabaseNameIfCrossDatabaseQuery($query)
     * @method static mixed addSelect($column)
     * @method static mixed distinct()
     * @method static mixed from($table, $as)
     * @method static mixed useIndex($index)
     * @method static mixed forceIndex($index)
     * @method static mixed ignoreIndex($index)
     * @method static mixed join($table, $first, $operator, $second, $type, $where)
     * @method static mixed joinWhere($table, $first, $operator, $second, $type)
     * @method static mixed joinSub($query, $as, $first, $operator, $second, $type, $where)
     * @method static mixed joinLateral($query, string $as, string $type)
     * @method static mixed leftJoinLateral($query, string $as)
     * @method static mixed leftJoin($table, $first, $operator, $second)
     * @method static mixed leftJoinWhere($table, $first, $operator, $second)
     * @method static mixed leftJoinSub($query, $as, $first, $operator, $second)
     * @method static mixed rightJoin($table, $first, $operator, $second)
     * @method static mixed rightJoinWhere($table, $first, $operator, $second)
     * @method static mixed rightJoinSub($query, $as, $first, $operator, $second)
     * @method static mixed crossJoin($table, $first, $operator, $second)
     * @method static mixed crossJoinSub($query, $as)
     * @method static mixed newJoinClause(self $parentQuery, $type, $table)
     * @method static mixed newJoinLateralClause(self $parentQuery, $type, $table)
     * @method static mixed mergeWheres($wheres, $bindings)
     * @method static mixed where($column, $operator, $value, $boolean)
     * @method static mixed addArrayOfWheres($column, $boolean, $method)
     * @method static mixed prepareValueAndOperator($value, $operator, $useDefault)
     * @method static mixed invalidOperatorAndValue($operator, $value)
     * @method static mixed invalidOperator($operator)
     * @method static mixed isBitwiseOperator($operator)
     * @method static mixed orWhere($column, $operator, $value)
     * @method static mixed whereNot($column, $operator, $value, $boolean)
     * @method static mixed orWhereNot($column, $operator, $value)
     * @method static mixed whereColumn($first, $operator, $second, $boolean)
     * @method static mixed orWhereColumn($first, $operator, $second)
     * @method static mixed whereRaw($sql, $bindings, $boolean)
     * @method static mixed orWhereRaw($sql, $bindings)
     * @method static mixed whereLike($column, $value, $caseSensitive, $boolean, $not)
     * @method static mixed orWhereLike($column, $value, $caseSensitive)
     * @method static mixed whereNotLike($column, $value, $caseSensitive, $boolean)
     * @method static mixed orWhereNotLike($column, $value, $caseSensitive)
     * @method static mixed whereIn($column, $values, $boolean, $not)
     * @method static mixed orWhereIn($column, $values)
     * @method static mixed whereNotIn($column, $values, $boolean)
     * @method static mixed orWhereNotIn($column, $values)
     * @method static mixed whereIntegerInRaw($column, $values, $boolean, $not)
     * @method static mixed orWhereIntegerInRaw($column, $values)
     * @method static mixed whereIntegerNotInRaw($column, $values, $boolean)
     * @method static mixed orWhereIntegerNotInRaw($column, $values)
     * @method static mixed whereNull($columns, $boolean, $not)
     * @method static mixed orWhereNull($column)
     * @method static mixed whereNotNull($columns, $boolean)
     * @method static mixed whereBetween($column, iterable $values, $boolean, $not)
     * @method static mixed whereBetweenColumns($column, array $values, $boolean, $not)
     * @method static mixed orWhereBetween($column, iterable $values)
     * @method static mixed orWhereBetweenColumns($column, array $values)
     * @method static mixed whereNotBetween($column, iterable $values, $boolean)
     * @method static mixed whereNotBetweenColumns($column, array $values, $boolean)
     * @method static mixed orWhereNotBetween($column, iterable $values)
     * @method static mixed orWhereNotBetweenColumns($column, array $values)
     * @method static mixed orWhereNotNull($column)
     * @method static mixed whereDate($column, $operator, $value, $boolean)
     * @method static mixed orWhereDate($column, $operator, $value)
     * @method static mixed whereTime($column, $operator, $value, $boolean)
     * @method static mixed orWhereTime($column, $operator, $value)
     * @method static mixed whereDay($column, $operator, $value, $boolean)
     * @method static mixed orWhereDay($column, $operator, $value)
     * @method static mixed whereMonth($column, $operator, $value, $boolean)
     * @method static mixed orWhereMonth($column, $operator, $value)
     * @method static mixed whereYear($column, $operator, $value, $boolean)
     * @method static mixed orWhereYear($column, $operator, $value)
     * @method static mixed addDateBasedWhere($type, $column, $operator, $value, $boolean)
     * @method static mixed whereNested(Closure $callback, $boolean)
     * @method static mixed forNestedWhere()
     * @method static mixed addNestedWhereQuery($query, $boolean)
     * @method static mixed whereSub($column, $operator, $callback, $boolean)
     * @method static mixed whereExists($callback, $boolean, $not)
     * @method static mixed orWhereExists($callback, $not)
     * @method static mixed whereNotExists($callback, $boolean)
     * @method static mixed orWhereNotExists($callback)
     * @method static mixed addWhereExistsQuery(self $query, $boolean, $not)
     * @method static mixed whereRowValues($columns, $operator, $values, $boolean)
     * @method static mixed orWhereRowValues($columns, $operator, $values)
     * @method static mixed whereJsonContains($column, $value, $boolean, $not)
     * @method static mixed orWhereJsonContains($column, $value)
     * @method static mixed whereJsonDoesntContain($column, $value, $boolean)
     * @method static mixed orWhereJsonDoesntContain($column, $value)
     * @method static mixed whereJsonOverlaps($column, $value, $boolean, $not)
     * @method static mixed orWhereJsonOverlaps($column, $value)
     * @method static mixed whereJsonDoesntOverlap($column, $value, $boolean)
     * @method static mixed orWhereJsonDoesntOverlap($column, $value)
     * @method static mixed whereJsonContainsKey($column, $boolean, $not)
     * @method static mixed orWhereJsonContainsKey($column)
     * @method static mixed whereJsonDoesntContainKey($column, $boolean)
     * @method static mixed orWhereJsonDoesntContainKey($column)
     * @method static mixed whereJsonLength($column, $operator, $value, $boolean)
     * @method static mixed orWhereJsonLength($column, $operator, $value)
     * @method static mixed dynamicWhere($method, $parameters)
     * @method static mixed addDynamic($segment, $connector, $parameters, $index)
     * @method static mixed whereFullText($columns, $value, array $options, $boolean)
     * @method static mixed orWhereFullText($columns, $value, array $options)
     * @method static mixed whereAll($columns, $operator, $value, $boolean)
     * @method static mixed orWhereAll($columns, $operator, $value)
     * @method static mixed whereAny($columns, $operator, $value, $boolean)
     * @method static mixed orWhereAny($columns, $operator, $value)
     * @method static mixed whereNone($columns, $operator, $value, $boolean)
     * @method static mixed orWhereNone($columns, $operator, $value)
     * @method static mixed groupBy($groups)
     * @method static mixed groupByRaw($sql, array $bindings)
     * @method static mixed having($column, $operator, $value, $boolean)
     * @method static mixed orHaving($column, $operator, $value)
     * @method static mixed havingNested(Closure $callback, $boolean)
     * @method static mixed addNestedHavingQuery($query, $boolean)
     * @method static mixed havingNull($columns, $boolean, $not)
     * @method static mixed orHavingNull($column)
     * @method static mixed havingNotNull($columns, $boolean)
     * @method static mixed orHavingNotNull($column)
     * @method static mixed havingBetween($column, iterable $values, $boolean, $not)
     * @method static mixed havingRaw($sql, array $bindings, $boolean)
     * @method static mixed orHavingRaw($sql, array $bindings)
     * @method static mixed orderBy($column, $direction)
     * @method static mixed orderByDesc($column)
     * @method static mixed latest($column)
     * @method static mixed oldest($column)
     * @method static mixed inRandomOrder($seed)
     * @method static mixed orderByRaw($sql, $bindings)
     * @method static mixed skip($value)
     * @method static mixed offset($value)
     * @method static mixed take($value)
     * @method static mixed limit($value)
     * @method static mixed groupLimit($value, $column)
     * @method static mixed forPage($page, $perPage)
     * @method static mixed forPageBeforeId($perPage, $lastId, $column)
     * @method static mixed forPageAfterId($perPage, $lastId, $column)
     * @method static mixed reorder($column, $direction)
     * @method static mixed removeExistingOrdersFor($column)
     * @method static mixed union($query, $all)
     * @method static mixed unionAll($query)
     * @method static mixed lock($value)
     * @method static mixed lockForUpdate()
     * @method static mixed sharedLock()
     * @method static mixed beforeQuery(callable $callback)
     * @method static mixed applyBeforeQueryCallbacks()
     * @method static mixed afterQuery(Closure $callback)
     * @method static mixed applyAfterQueryCallbacks($result)
     * @method static mixed toSql()
     * @method static mixed toRawSql()
     * @method static mixed find($id, $columns)
     * @method static mixed findOr($id, $columns, Closure $callback)
     * @method static mixed value($column)
     * @method static mixed rawValue(string $expression, array $bindings)
     * @method static mixed soleValue($column)
     * @method static mixed get($columns)
     * @method static mixed runSelect()
     * @method static mixed withoutGroupLimitKeys($items)
     * @method static mixed paginate($perPage, $columns, $pageName, $page, $total)
     * @method static mixed simplePaginate($perPage, $columns, $pageName, $page)
     * @method static mixed cursorPaginate($perPage, $columns, $cursorName, $cursor)
     * @method static mixed ensureOrderForCursorPagination($shouldReverse)
     * @method static mixed getCountForPagination($columns)
     * @method static mixed runPaginationCountQuery($columns)
     * @method static mixed cloneForPaginationCount()
     * @method static mixed withoutSelectAliases(array $columns)
     * @method static mixed cursor()
     * @method static mixed enforceOrderBy()
     * @method static mixed pluck($column, $key)
     * @method static mixed stripTableForPluck($column)
     * @method static mixed pluckFromObjectColumn($queryResult, $column, $key)
     * @method static mixed pluckFromArrayColumn($queryResult, $column, $key)
     * @method static mixed implode($column, $glue)
     * @method static mixed exists()
     * @method static mixed doesntExist()
     * @method static mixed existsOr(Closure $callback)
     * @method static mixed doesntExistOr(Closure $callback)
     * @method static mixed count($columns)
     * @method static mixed min($column)
     * @method static mixed max($column)
     * @method static mixed sum($column)
     * @method static mixed avg($column)
     * @method static mixed average($column)
     * @method static mixed aggregate($function, $columns)
     * @method static mixed numericAggregate($function, $columns)
     * @method static mixed setAggregate($function, $columns)
     * @method static mixed onceWithColumns($columns, $callback)
     * @method static mixed insert(array $values)
     * @method static mixed insertOrIgnore(array $values)
     * @method static mixed insertGetId(array $values, $sequence)
     * @method static mixed insertUsing(array $columns, $query)
     * @method static mixed insertOrIgnoreUsing(array $columns, $query)
     * @method static mixed update(array $values)
     * @method static mixed updateFrom(array $values)
     * @method static mixed updateOrInsert(array $attributes, callable|array $values)
     * @method static mixed upsert(array $values, $uniqueBy, $update)
     * @method static mixed increment($column, $amount, array $extra)
     * @method static mixed incrementEach(array $columns, array $extra)
     * @method static mixed decrement($column, $amount, array $extra)
     * @method static mixed decrementEach(array $columns, array $extra)
     * @method static mixed delete($id)
     * @method static mixed truncate()
     * @method static mixed newQuery()
     * @method static mixed forSubQuery()
     * @method static mixed getColumns()
     * @method static mixed raw($value)
     * @method static mixed getUnionBuilders()
     * @method static mixed getBindings()
     * @method static mixed getRawBindings()
     * @method static mixed setBindings(array $bindings, $type)
     * @method static mixed addBinding($value, $type)
     * @method static mixed castBinding($value)
     * @method static mixed mergeBindings(self $query)
     * @method static mixed cleanBindings(array $bindings)
     * @method static mixed flattenValue($value)
     * @method static mixed defaultKeyName()
     * @method static mixed getConnection()
     * @method static mixed getProcessor()
     * @method static mixed getGrammar()
     * @method static mixed useWritePdo()
     * @method static mixed isQueryable($value)
     * @method static mixed clone()
     * @method static mixed cloneWithout(array $properties)
     * @method static mixed cloneWithoutBindings(array $except)
     * @method static mixed dump($args)
     * @method static mixed dumpRawSql()
     * @method static mixed dd()
     * @method static mixed ddRawSql()
     * @method static mixed wherePast($columns)
     * @method static mixed whereNowOrPast($columns)
     * @method static mixed orWherePast($columns)
     * @method static mixed orWhereNowOrPast($columns)
     * @method static mixed whereFuture($columns)
     * @method static mixed whereNowOrFuture($columns)
     * @method static mixed orWhereFuture($columns)
     * @method static mixed orWhereNowOrFuture($columns)
     * @method static mixed wherePastOrFuture($columns, $operator, $boolean)
     * @method static mixed whereToday($columns, $boolean)
     * @method static mixed whereBeforeToday($columns)
     * @method static mixed whereTodayOrBefore($columns)
     * @method static mixed whereAfterToday($columns)
     * @method static mixed whereTodayOrAfter($columns)
     * @method static mixed orWhereToday($columns)
     * @method static mixed orWhereBeforeToday($columns)
     * @method static mixed orWhereTodayOrBefore($columns)
     * @method static mixed orWhereAfterToday($columns)
     * @method static mixed orWhereTodayOrAfter($columns)
     * @method static mixed whereTodayBeforeOrAfter($columns, $operator, $boolean)
     * @method static mixed chunk($count, callable $callback)
     * @method static mixed chunkMap(callable $callback, $count)
     * @method static mixed each(callable $callback, $count)
     * @method static mixed chunkById($count, callable $callback, $column, $alias)
     * @method static mixed chunkByIdDesc($count, callable $callback, $column, $alias)
     * @method static mixed orderedChunkById($count, callable $callback, $column, $alias, $descending)
     * @method static mixed eachById(callable $callback, $count, $column, $alias)
     * @method static mixed lazy($chunkSize)
     * @method static mixed lazyById($chunkSize, $column, $alias)
     * @method static mixed lazyByIdDesc($chunkSize, $column, $alias)
     * @method static mixed orderedLazyById($chunkSize, $column, $alias, $descending)
     * @method static mixed first($columns)
     * @method static mixed firstOrFail($columns, $message)
     * @method static mixed sole($columns)
     * @method static mixed paginateUsingCursor($perPage, $columns, $cursorName, $cursor)
     * @method static mixed getOriginalColumnNameForCursorPagination($builder, string $parameter)
     * @method static mixed paginator($items, $total, $perPage, $currentPage, $options)
     * @method static mixed simplePaginator($items, $perPage, $currentPage, $options)
     * @method static mixed cursorPaginator($items, $perPage, $cursor, $options)
     * @method static mixed tap($callback)
     * @method static mixed when($value, callable $callback, callable $default)
     * @method static mixed unless($value, callable $callback, callable $default)
     * @method static mixed explain()
     * @method static mixed forwardCallTo($object, $method, $parameters)
     * @method static mixed forwardDecoratedCallTo($object, $method, $parameters)
     * @method static mixed throwBadMethodCallException($method)
     * @method static mixed macro($name, $macro)
     * @method static mixed mixin($mixin, $replace)
     * @method static mixed hasMacro($name)
     * @method static mixed flushMacros()
     * @method static mixed macroCall($method, $parameters)
     * @mixin \Illuminate\Database\Query\Builder
     */
    class Booking extends \Illuminate\Database\Eloquent\Model
    {
        //
    }

    /**
     * App\Models\Branch
     *
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property string|null $manager_phone
     * @property string|null $manager_name
     * @property array|null $operating_hours
     * @property string|null $description
     * @property boolean $status
     * @property string|null $email
     * @property string|null $phone
     * @property decimal:8 $longitude
     * @property decimal:8 $latitude
     * @property string $address
     * @property string $name
     * @property int $id
     * @property-read mixed $formatted_operating_hours
     * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Booking> $bookings
     * @property-read int|null $bookings_count
     * @method static \Illuminate\Database\Eloquent\Builder<Branch>|Branch whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Branch>|Branch whereName($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Branch>|Branch whereAddress($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Branch>|Branch whereLatitude($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Branch>|Branch whereLongitude($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Branch>|Branch wherePhone($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Branch>|Branch whereEmail($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Branch>|Branch whereStatus($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Branch>|Branch whereDescription($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Branch>|Branch whereOperatingHours($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Branch>|Branch whereManagerName($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Branch>|Branch whereManagerPhone($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Branch>|Branch whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Branch>|Branch whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Branch>|Branch active()
     * @method static \Illuminate\Database\Eloquent\Builder<Branch>|Branch inactive()
     * @method static \Illuminate\Database\Eloquent\Builder<Branch>|Branch newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<Branch>|Branch newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<Branch>|Branch query()
     * @method static mixed select($columns)
     * @method static mixed selectSub($query, $as)
     * @method static mixed selectRaw($expression, array $bindings)
     * @method static mixed fromSub($query, $as)
     * @method static mixed fromRaw($expression, $bindings)
     * @method static mixed createSub($query)
     * @method static mixed parseSub($query)
     * @method static mixed prependDatabaseNameIfCrossDatabaseQuery($query)
     * @method static mixed addSelect($column)
     * @method static mixed distinct()
     * @method static mixed from($table, $as)
     * @method static mixed useIndex($index)
     * @method static mixed forceIndex($index)
     * @method static mixed ignoreIndex($index)
     * @method static mixed join($table, $first, $operator, $second, $type, $where)
     * @method static mixed joinWhere($table, $first, $operator, $second, $type)
     * @method static mixed joinSub($query, $as, $first, $operator, $second, $type, $where)
     * @method static mixed joinLateral($query, string $as, string $type)
     * @method static mixed leftJoinLateral($query, string $as)
     * @method static mixed leftJoin($table, $first, $operator, $second)
     * @method static mixed leftJoinWhere($table, $first, $operator, $second)
     * @method static mixed leftJoinSub($query, $as, $first, $operator, $second)
     * @method static mixed rightJoin($table, $first, $operator, $second)
     * @method static mixed rightJoinWhere($table, $first, $operator, $second)
     * @method static mixed rightJoinSub($query, $as, $first, $operator, $second)
     * @method static mixed crossJoin($table, $first, $operator, $second)
     * @method static mixed crossJoinSub($query, $as)
     * @method static mixed newJoinClause(self $parentQuery, $type, $table)
     * @method static mixed newJoinLateralClause(self $parentQuery, $type, $table)
     * @method static mixed mergeWheres($wheres, $bindings)
     * @method static mixed where($column, $operator, $value, $boolean)
     * @method static mixed addArrayOfWheres($column, $boolean, $method)
     * @method static mixed prepareValueAndOperator($value, $operator, $useDefault)
     * @method static mixed invalidOperatorAndValue($operator, $value)
     * @method static mixed invalidOperator($operator)
     * @method static mixed isBitwiseOperator($operator)
     * @method static mixed orWhere($column, $operator, $value)
     * @method static mixed whereNot($column, $operator, $value, $boolean)
     * @method static mixed orWhereNot($column, $operator, $value)
     * @method static mixed whereColumn($first, $operator, $second, $boolean)
     * @method static mixed orWhereColumn($first, $operator, $second)
     * @method static mixed whereRaw($sql, $bindings, $boolean)
     * @method static mixed orWhereRaw($sql, $bindings)
     * @method static mixed whereLike($column, $value, $caseSensitive, $boolean, $not)
     * @method static mixed orWhereLike($column, $value, $caseSensitive)
     * @method static mixed whereNotLike($column, $value, $caseSensitive, $boolean)
     * @method static mixed orWhereNotLike($column, $value, $caseSensitive)
     * @method static mixed whereIn($column, $values, $boolean, $not)
     * @method static mixed orWhereIn($column, $values)
     * @method static mixed whereNotIn($column, $values, $boolean)
     * @method static mixed orWhereNotIn($column, $values)
     * @method static mixed whereIntegerInRaw($column, $values, $boolean, $not)
     * @method static mixed orWhereIntegerInRaw($column, $values)
     * @method static mixed whereIntegerNotInRaw($column, $values, $boolean)
     * @method static mixed orWhereIntegerNotInRaw($column, $values)
     * @method static mixed whereNull($columns, $boolean, $not)
     * @method static mixed orWhereNull($column)
     * @method static mixed whereNotNull($columns, $boolean)
     * @method static mixed whereBetween($column, iterable $values, $boolean, $not)
     * @method static mixed whereBetweenColumns($column, array $values, $boolean, $not)
     * @method static mixed orWhereBetween($column, iterable $values)
     * @method static mixed orWhereBetweenColumns($column, array $values)
     * @method static mixed whereNotBetween($column, iterable $values, $boolean)
     * @method static mixed whereNotBetweenColumns($column, array $values, $boolean)
     * @method static mixed orWhereNotBetween($column, iterable $values)
     * @method static mixed orWhereNotBetweenColumns($column, array $values)
     * @method static mixed orWhereNotNull($column)
     * @method static mixed whereDate($column, $operator, $value, $boolean)
     * @method static mixed orWhereDate($column, $operator, $value)
     * @method static mixed whereTime($column, $operator, $value, $boolean)
     * @method static mixed orWhereTime($column, $operator, $value)
     * @method static mixed whereDay($column, $operator, $value, $boolean)
     * @method static mixed orWhereDay($column, $operator, $value)
     * @method static mixed whereMonth($column, $operator, $value, $boolean)
     * @method static mixed orWhereMonth($column, $operator, $value)
     * @method static mixed whereYear($column, $operator, $value, $boolean)
     * @method static mixed orWhereYear($column, $operator, $value)
     * @method static mixed addDateBasedWhere($type, $column, $operator, $value, $boolean)
     * @method static mixed whereNested(Closure $callback, $boolean)
     * @method static mixed forNestedWhere()
     * @method static mixed addNestedWhereQuery($query, $boolean)
     * @method static mixed whereSub($column, $operator, $callback, $boolean)
     * @method static mixed whereExists($callback, $boolean, $not)
     * @method static mixed orWhereExists($callback, $not)
     * @method static mixed whereNotExists($callback, $boolean)
     * @method static mixed orWhereNotExists($callback)
     * @method static mixed addWhereExistsQuery(self $query, $boolean, $not)
     * @method static mixed whereRowValues($columns, $operator, $values, $boolean)
     * @method static mixed orWhereRowValues($columns, $operator, $values)
     * @method static mixed whereJsonContains($column, $value, $boolean, $not)
     * @method static mixed orWhereJsonContains($column, $value)
     * @method static mixed whereJsonDoesntContain($column, $value, $boolean)
     * @method static mixed orWhereJsonDoesntContain($column, $value)
     * @method static mixed whereJsonOverlaps($column, $value, $boolean, $not)
     * @method static mixed orWhereJsonOverlaps($column, $value)
     * @method static mixed whereJsonDoesntOverlap($column, $value, $boolean)
     * @method static mixed orWhereJsonDoesntOverlap($column, $value)
     * @method static mixed whereJsonContainsKey($column, $boolean, $not)
     * @method static mixed orWhereJsonContainsKey($column)
     * @method static mixed whereJsonDoesntContainKey($column, $boolean)
     * @method static mixed orWhereJsonDoesntContainKey($column)
     * @method static mixed whereJsonLength($column, $operator, $value, $boolean)
     * @method static mixed orWhereJsonLength($column, $operator, $value)
     * @method static mixed dynamicWhere($method, $parameters)
     * @method static mixed addDynamic($segment, $connector, $parameters, $index)
     * @method static mixed whereFullText($columns, $value, array $options, $boolean)
     * @method static mixed orWhereFullText($columns, $value, array $options)
     * @method static mixed whereAll($columns, $operator, $value, $boolean)
     * @method static mixed orWhereAll($columns, $operator, $value)
     * @method static mixed whereAny($columns, $operator, $value, $boolean)
     * @method static mixed orWhereAny($columns, $operator, $value)
     * @method static mixed whereNone($columns, $operator, $value, $boolean)
     * @method static mixed orWhereNone($columns, $operator, $value)
     * @method static mixed groupBy($groups)
     * @method static mixed groupByRaw($sql, array $bindings)
     * @method static mixed having($column, $operator, $value, $boolean)
     * @method static mixed orHaving($column, $operator, $value)
     * @method static mixed havingNested(Closure $callback, $boolean)
     * @method static mixed addNestedHavingQuery($query, $boolean)
     * @method static mixed havingNull($columns, $boolean, $not)
     * @method static mixed orHavingNull($column)
     * @method static mixed havingNotNull($columns, $boolean)
     * @method static mixed orHavingNotNull($column)
     * @method static mixed havingBetween($column, iterable $values, $boolean, $not)
     * @method static mixed havingRaw($sql, array $bindings, $boolean)
     * @method static mixed orHavingRaw($sql, array $bindings)
     * @method static mixed orderBy($column, $direction)
     * @method static mixed orderByDesc($column)
     * @method static mixed latest($column)
     * @method static mixed oldest($column)
     * @method static mixed inRandomOrder($seed)
     * @method static mixed orderByRaw($sql, $bindings)
     * @method static mixed skip($value)
     * @method static mixed offset($value)
     * @method static mixed take($value)
     * @method static mixed limit($value)
     * @method static mixed groupLimit($value, $column)
     * @method static mixed forPage($page, $perPage)
     * @method static mixed forPageBeforeId($perPage, $lastId, $column)
     * @method static mixed forPageAfterId($perPage, $lastId, $column)
     * @method static mixed reorder($column, $direction)
     * @method static mixed removeExistingOrdersFor($column)
     * @method static mixed union($query, $all)
     * @method static mixed unionAll($query)
     * @method static mixed lock($value)
     * @method static mixed lockForUpdate()
     * @method static mixed sharedLock()
     * @method static mixed beforeQuery(callable $callback)
     * @method static mixed applyBeforeQueryCallbacks()
     * @method static mixed afterQuery(Closure $callback)
     * @method static mixed applyAfterQueryCallbacks($result)
     * @method static mixed toSql()
     * @method static mixed toRawSql()
     * @method static mixed find($id, $columns)
     * @method static mixed findOr($id, $columns, Closure $callback)
     * @method static mixed value($column)
     * @method static mixed rawValue(string $expression, array $bindings)
     * @method static mixed soleValue($column)
     * @method static mixed get($columns)
     * @method static mixed runSelect()
     * @method static mixed withoutGroupLimitKeys($items)
     * @method static mixed paginate($perPage, $columns, $pageName, $page, $total)
     * @method static mixed simplePaginate($perPage, $columns, $pageName, $page)
     * @method static mixed cursorPaginate($perPage, $columns, $cursorName, $cursor)
     * @method static mixed ensureOrderForCursorPagination($shouldReverse)
     * @method static mixed getCountForPagination($columns)
     * @method static mixed runPaginationCountQuery($columns)
     * @method static mixed cloneForPaginationCount()
     * @method static mixed withoutSelectAliases(array $columns)
     * @method static mixed cursor()
     * @method static mixed enforceOrderBy()
     * @method static mixed pluck($column, $key)
     * @method static mixed stripTableForPluck($column)
     * @method static mixed pluckFromObjectColumn($queryResult, $column, $key)
     * @method static mixed pluckFromArrayColumn($queryResult, $column, $key)
     * @method static mixed implode($column, $glue)
     * @method static mixed exists()
     * @method static mixed doesntExist()
     * @method static mixed existsOr(Closure $callback)
     * @method static mixed doesntExistOr(Closure $callback)
     * @method static mixed count($columns)
     * @method static mixed min($column)
     * @method static mixed max($column)
     * @method static mixed sum($column)
     * @method static mixed avg($column)
     * @method static mixed average($column)
     * @method static mixed aggregate($function, $columns)
     * @method static mixed numericAggregate($function, $columns)
     * @method static mixed setAggregate($function, $columns)
     * @method static mixed onceWithColumns($columns, $callback)
     * @method static mixed insert(array $values)
     * @method static mixed insertOrIgnore(array $values)
     * @method static mixed insertGetId(array $values, $sequence)
     * @method static mixed insertUsing(array $columns, $query)
     * @method static mixed insertOrIgnoreUsing(array $columns, $query)
     * @method static mixed update(array $values)
     * @method static mixed updateFrom(array $values)
     * @method static mixed updateOrInsert(array $attributes, callable|array $values)
     * @method static mixed upsert(array $values, $uniqueBy, $update)
     * @method static mixed increment($column, $amount, array $extra)
     * @method static mixed incrementEach(array $columns, array $extra)
     * @method static mixed decrement($column, $amount, array $extra)
     * @method static mixed decrementEach(array $columns, array $extra)
     * @method static mixed delete($id)
     * @method static mixed truncate()
     * @method static mixed newQuery()
     * @method static mixed forSubQuery()
     * @method static mixed getColumns()
     * @method static mixed raw($value)
     * @method static mixed getUnionBuilders()
     * @method static mixed getBindings()
     * @method static mixed getRawBindings()
     * @method static mixed setBindings(array $bindings, $type)
     * @method static mixed addBinding($value, $type)
     * @method static mixed castBinding($value)
     * @method static mixed mergeBindings(self $query)
     * @method static mixed cleanBindings(array $bindings)
     * @method static mixed flattenValue($value)
     * @method static mixed defaultKeyName()
     * @method static mixed getConnection()
     * @method static mixed getProcessor()
     * @method static mixed getGrammar()
     * @method static mixed useWritePdo()
     * @method static mixed isQueryable($value)
     * @method static mixed clone()
     * @method static mixed cloneWithout(array $properties)
     * @method static mixed cloneWithoutBindings(array $except)
     * @method static mixed dump($args)
     * @method static mixed dumpRawSql()
     * @method static mixed dd()
     * @method static mixed ddRawSql()
     * @method static mixed wherePast($columns)
     * @method static mixed whereNowOrPast($columns)
     * @method static mixed orWherePast($columns)
     * @method static mixed orWhereNowOrPast($columns)
     * @method static mixed whereFuture($columns)
     * @method static mixed whereNowOrFuture($columns)
     * @method static mixed orWhereFuture($columns)
     * @method static mixed orWhereNowOrFuture($columns)
     * @method static mixed wherePastOrFuture($columns, $operator, $boolean)
     * @method static mixed whereToday($columns, $boolean)
     * @method static mixed whereBeforeToday($columns)
     * @method static mixed whereTodayOrBefore($columns)
     * @method static mixed whereAfterToday($columns)
     * @method static mixed whereTodayOrAfter($columns)
     * @method static mixed orWhereToday($columns)
     * @method static mixed orWhereBeforeToday($columns)
     * @method static mixed orWhereTodayOrBefore($columns)
     * @method static mixed orWhereAfterToday($columns)
     * @method static mixed orWhereTodayOrAfter($columns)
     * @method static mixed whereTodayBeforeOrAfter($columns, $operator, $boolean)
     * @method static mixed chunk($count, callable $callback)
     * @method static mixed chunkMap(callable $callback, $count)
     * @method static mixed each(callable $callback, $count)
     * @method static mixed chunkById($count, callable $callback, $column, $alias)
     * @method static mixed chunkByIdDesc($count, callable $callback, $column, $alias)
     * @method static mixed orderedChunkById($count, callable $callback, $column, $alias, $descending)
     * @method static mixed eachById(callable $callback, $count, $column, $alias)
     * @method static mixed lazy($chunkSize)
     * @method static mixed lazyById($chunkSize, $column, $alias)
     * @method static mixed lazyByIdDesc($chunkSize, $column, $alias)
     * @method static mixed orderedLazyById($chunkSize, $column, $alias, $descending)
     * @method static mixed first($columns)
     * @method static mixed firstOrFail($columns, $message)
     * @method static mixed sole($columns)
     * @method static mixed paginateUsingCursor($perPage, $columns, $cursorName, $cursor)
     * @method static mixed getOriginalColumnNameForCursorPagination($builder, string $parameter)
     * @method static mixed paginator($items, $total, $perPage, $currentPage, $options)
     * @method static mixed simplePaginator($items, $perPage, $currentPage, $options)
     * @method static mixed cursorPaginator($items, $perPage, $cursor, $options)
     * @method static mixed tap($callback)
     * @method static mixed when($value, callable $callback, callable $default)
     * @method static mixed unless($value, callable $callback, callable $default)
     * @method static mixed explain()
     * @method static mixed forwardCallTo($object, $method, $parameters)
     * @method static mixed forwardDecoratedCallTo($object, $method, $parameters)
     * @method static mixed throwBadMethodCallException($method)
     * @method static mixed macro($name, $macro)
     * @method static mixed mixin($mixin, $replace)
     * @method static mixed hasMacro($name)
     * @method static mixed flushMacros()
     * @method static mixed macroCall($method, $parameters)
     * @mixin \Illuminate\Database\Query\Builder
     */
    class Branch extends \Illuminate\Database\Eloquent\Model
    {
        //
    }

    /**
     * App\Models\Notification
     *
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $read_at
     * @property array|null $data
     * @property string $type
     * @property string $message
     * @property string $title
     * @property mixed $user_id
     * @property string $id
     * @property-read mixed $icon
     * @property-read mixed $color
     * @property-read \App\Models\User $user
     * @method static \Illuminate\Database\Eloquent\Builder<Notification>|Notification whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Notification>|Notification whereUserId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Notification>|Notification whereTitle($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Notification>|Notification whereMessage($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Notification>|Notification whereType($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Notification>|Notification whereData($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Notification>|Notification whereReadAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Notification>|Notification whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Notification>|Notification whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Notification>|Notification unread()
     * @method static \Illuminate\Database\Eloquent\Builder<Notification>|Notification read()
     * @method static \Illuminate\Database\Eloquent\Builder<Notification>|Notification ofType()
     * @method static \Illuminate\Database\Eloquent\Builder<Notification>|Notification newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<Notification>|Notification newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<Notification>|Notification query()
     * @method static mixed select($columns)
     * @method static mixed selectSub($query, $as)
     * @method static mixed selectRaw($expression, array $bindings)
     * @method static mixed fromSub($query, $as)
     * @method static mixed fromRaw($expression, $bindings)
     * @method static mixed createSub($query)
     * @method static mixed parseSub($query)
     * @method static mixed prependDatabaseNameIfCrossDatabaseQuery($query)
     * @method static mixed addSelect($column)
     * @method static mixed distinct()
     * @method static mixed from($table, $as)
     * @method static mixed useIndex($index)
     * @method static mixed forceIndex($index)
     * @method static mixed ignoreIndex($index)
     * @method static mixed join($table, $first, $operator, $second, $type, $where)
     * @method static mixed joinWhere($table, $first, $operator, $second, $type)
     * @method static mixed joinSub($query, $as, $first, $operator, $second, $type, $where)
     * @method static mixed joinLateral($query, string $as, string $type)
     * @method static mixed leftJoinLateral($query, string $as)
     * @method static mixed leftJoin($table, $first, $operator, $second)
     * @method static mixed leftJoinWhere($table, $first, $operator, $second)
     * @method static mixed leftJoinSub($query, $as, $first, $operator, $second)
     * @method static mixed rightJoin($table, $first, $operator, $second)
     * @method static mixed rightJoinWhere($table, $first, $operator, $second)
     * @method static mixed rightJoinSub($query, $as, $first, $operator, $second)
     * @method static mixed crossJoin($table, $first, $operator, $second)
     * @method static mixed crossJoinSub($query, $as)
     * @method static mixed newJoinClause(self $parentQuery, $type, $table)
     * @method static mixed newJoinLateralClause(self $parentQuery, $type, $table)
     * @method static mixed mergeWheres($wheres, $bindings)
     * @method static mixed where($column, $operator, $value, $boolean)
     * @method static mixed addArrayOfWheres($column, $boolean, $method)
     * @method static mixed prepareValueAndOperator($value, $operator, $useDefault)
     * @method static mixed invalidOperatorAndValue($operator, $value)
     * @method static mixed invalidOperator($operator)
     * @method static mixed isBitwiseOperator($operator)
     * @method static mixed orWhere($column, $operator, $value)
     * @method static mixed whereNot($column, $operator, $value, $boolean)
     * @method static mixed orWhereNot($column, $operator, $value)
     * @method static mixed whereColumn($first, $operator, $second, $boolean)
     * @method static mixed orWhereColumn($first, $operator, $second)
     * @method static mixed whereRaw($sql, $bindings, $boolean)
     * @method static mixed orWhereRaw($sql, $bindings)
     * @method static mixed whereLike($column, $value, $caseSensitive, $boolean, $not)
     * @method static mixed orWhereLike($column, $value, $caseSensitive)
     * @method static mixed whereNotLike($column, $value, $caseSensitive, $boolean)
     * @method static mixed orWhereNotLike($column, $value, $caseSensitive)
     * @method static mixed whereIn($column, $values, $boolean, $not)
     * @method static mixed orWhereIn($column, $values)
     * @method static mixed whereNotIn($column, $values, $boolean)
     * @method static mixed orWhereNotIn($column, $values)
     * @method static mixed whereIntegerInRaw($column, $values, $boolean, $not)
     * @method static mixed orWhereIntegerInRaw($column, $values)
     * @method static mixed whereIntegerNotInRaw($column, $values, $boolean)
     * @method static mixed orWhereIntegerNotInRaw($column, $values)
     * @method static mixed whereNull($columns, $boolean, $not)
     * @method static mixed orWhereNull($column)
     * @method static mixed whereNotNull($columns, $boolean)
     * @method static mixed whereBetween($column, iterable $values, $boolean, $not)
     * @method static mixed whereBetweenColumns($column, array $values, $boolean, $not)
     * @method static mixed orWhereBetween($column, iterable $values)
     * @method static mixed orWhereBetweenColumns($column, array $values)
     * @method static mixed whereNotBetween($column, iterable $values, $boolean)
     * @method static mixed whereNotBetweenColumns($column, array $values, $boolean)
     * @method static mixed orWhereNotBetween($column, iterable $values)
     * @method static mixed orWhereNotBetweenColumns($column, array $values)
     * @method static mixed orWhereNotNull($column)
     * @method static mixed whereDate($column, $operator, $value, $boolean)
     * @method static mixed orWhereDate($column, $operator, $value)
     * @method static mixed whereTime($column, $operator, $value, $boolean)
     * @method static mixed orWhereTime($column, $operator, $value)
     * @method static mixed whereDay($column, $operator, $value, $boolean)
     * @method static mixed orWhereDay($column, $operator, $value)
     * @method static mixed whereMonth($column, $operator, $value, $boolean)
     * @method static mixed orWhereMonth($column, $operator, $value)
     * @method static mixed whereYear($column, $operator, $value, $boolean)
     * @method static mixed orWhereYear($column, $operator, $value)
     * @method static mixed addDateBasedWhere($type, $column, $operator, $value, $boolean)
     * @method static mixed whereNested(Closure $callback, $boolean)
     * @method static mixed forNestedWhere()
     * @method static mixed addNestedWhereQuery($query, $boolean)
     * @method static mixed whereSub($column, $operator, $callback, $boolean)
     * @method static mixed whereExists($callback, $boolean, $not)
     * @method static mixed orWhereExists($callback, $not)
     * @method static mixed whereNotExists($callback, $boolean)
     * @method static mixed orWhereNotExists($callback)
     * @method static mixed addWhereExistsQuery(self $query, $boolean, $not)
     * @method static mixed whereRowValues($columns, $operator, $values, $boolean)
     * @method static mixed orWhereRowValues($columns, $operator, $values)
     * @method static mixed whereJsonContains($column, $value, $boolean, $not)
     * @method static mixed orWhereJsonContains($column, $value)
     * @method static mixed whereJsonDoesntContain($column, $value, $boolean)
     * @method static mixed orWhereJsonDoesntContain($column, $value)
     * @method static mixed whereJsonOverlaps($column, $value, $boolean, $not)
     * @method static mixed orWhereJsonOverlaps($column, $value)
     * @method static mixed whereJsonDoesntOverlap($column, $value, $boolean)
     * @method static mixed orWhereJsonDoesntOverlap($column, $value)
     * @method static mixed whereJsonContainsKey($column, $boolean, $not)
     * @method static mixed orWhereJsonContainsKey($column)
     * @method static mixed whereJsonDoesntContainKey($column, $boolean)
     * @method static mixed orWhereJsonDoesntContainKey($column)
     * @method static mixed whereJsonLength($column, $operator, $value, $boolean)
     * @method static mixed orWhereJsonLength($column, $operator, $value)
     * @method static mixed dynamicWhere($method, $parameters)
     * @method static mixed addDynamic($segment, $connector, $parameters, $index)
     * @method static mixed whereFullText($columns, $value, array $options, $boolean)
     * @method static mixed orWhereFullText($columns, $value, array $options)
     * @method static mixed whereAll($columns, $operator, $value, $boolean)
     * @method static mixed orWhereAll($columns, $operator, $value)
     * @method static mixed whereAny($columns, $operator, $value, $boolean)
     * @method static mixed orWhereAny($columns, $operator, $value)
     * @method static mixed whereNone($columns, $operator, $value, $boolean)
     * @method static mixed orWhereNone($columns, $operator, $value)
     * @method static mixed groupBy($groups)
     * @method static mixed groupByRaw($sql, array $bindings)
     * @method static mixed having($column, $operator, $value, $boolean)
     * @method static mixed orHaving($column, $operator, $value)
     * @method static mixed havingNested(Closure $callback, $boolean)
     * @method static mixed addNestedHavingQuery($query, $boolean)
     * @method static mixed havingNull($columns, $boolean, $not)
     * @method static mixed orHavingNull($column)
     * @method static mixed havingNotNull($columns, $boolean)
     * @method static mixed orHavingNotNull($column)
     * @method static mixed havingBetween($column, iterable $values, $boolean, $not)
     * @method static mixed havingRaw($sql, array $bindings, $boolean)
     * @method static mixed orHavingRaw($sql, array $bindings)
     * @method static mixed orderBy($column, $direction)
     * @method static mixed orderByDesc($column)
     * @method static mixed latest($column)
     * @method static mixed oldest($column)
     * @method static mixed inRandomOrder($seed)
     * @method static mixed orderByRaw($sql, $bindings)
     * @method static mixed skip($value)
     * @method static mixed offset($value)
     * @method static mixed take($value)
     * @method static mixed limit($value)
     * @method static mixed groupLimit($value, $column)
     * @method static mixed forPage($page, $perPage)
     * @method static mixed forPageBeforeId($perPage, $lastId, $column)
     * @method static mixed forPageAfterId($perPage, $lastId, $column)
     * @method static mixed reorder($column, $direction)
     * @method static mixed removeExistingOrdersFor($column)
     * @method static mixed union($query, $all)
     * @method static mixed unionAll($query)
     * @method static mixed lock($value)
     * @method static mixed lockForUpdate()
     * @method static mixed sharedLock()
     * @method static mixed beforeQuery(callable $callback)
     * @method static mixed applyBeforeQueryCallbacks()
     * @method static mixed afterQuery(Closure $callback)
     * @method static mixed applyAfterQueryCallbacks($result)
     * @method static mixed toSql()
     * @method static mixed toRawSql()
     * @method static mixed find($id, $columns)
     * @method static mixed findOr($id, $columns, Closure $callback)
     * @method static mixed value($column)
     * @method static mixed rawValue(string $expression, array $bindings)
     * @method static mixed soleValue($column)
     * @method static mixed get($columns)
     * @method static mixed runSelect()
     * @method static mixed withoutGroupLimitKeys($items)
     * @method static mixed paginate($perPage, $columns, $pageName, $page, $total)
     * @method static mixed simplePaginate($perPage, $columns, $pageName, $page)
     * @method static mixed cursorPaginate($perPage, $columns, $cursorName, $cursor)
     * @method static mixed ensureOrderForCursorPagination($shouldReverse)
     * @method static mixed getCountForPagination($columns)
     * @method static mixed runPaginationCountQuery($columns)
     * @method static mixed cloneForPaginationCount()
     * @method static mixed withoutSelectAliases(array $columns)
     * @method static mixed cursor()
     * @method static mixed enforceOrderBy()
     * @method static mixed pluck($column, $key)
     * @method static mixed stripTableForPluck($column)
     * @method static mixed pluckFromObjectColumn($queryResult, $column, $key)
     * @method static mixed pluckFromArrayColumn($queryResult, $column, $key)
     * @method static mixed implode($column, $glue)
     * @method static mixed exists()
     * @method static mixed doesntExist()
     * @method static mixed existsOr(Closure $callback)
     * @method static mixed doesntExistOr(Closure $callback)
     * @method static mixed count($columns)
     * @method static mixed min($column)
     * @method static mixed max($column)
     * @method static mixed sum($column)
     * @method static mixed avg($column)
     * @method static mixed average($column)
     * @method static mixed aggregate($function, $columns)
     * @method static mixed numericAggregate($function, $columns)
     * @method static mixed setAggregate($function, $columns)
     * @method static mixed onceWithColumns($columns, $callback)
     * @method static mixed insert(array $values)
     * @method static mixed insertOrIgnore(array $values)
     * @method static mixed insertGetId(array $values, $sequence)
     * @method static mixed insertUsing(array $columns, $query)
     * @method static mixed insertOrIgnoreUsing(array $columns, $query)
     * @method static mixed update(array $values)
     * @method static mixed updateFrom(array $values)
     * @method static mixed updateOrInsert(array $attributes, callable|array $values)
     * @method static mixed upsert(array $values, $uniqueBy, $update)
     * @method static mixed increment($column, $amount, array $extra)
     * @method static mixed incrementEach(array $columns, array $extra)
     * @method static mixed decrement($column, $amount, array $extra)
     * @method static mixed decrementEach(array $columns, array $extra)
     * @method static mixed delete($id)
     * @method static mixed truncate()
     * @method static mixed newQuery()
     * @method static mixed forSubQuery()
     * @method static mixed getColumns()
     * @method static mixed raw($value)
     * @method static mixed getUnionBuilders()
     * @method static mixed getBindings()
     * @method static mixed getRawBindings()
     * @method static mixed setBindings(array $bindings, $type)
     * @method static mixed addBinding($value, $type)
     * @method static mixed castBinding($value)
     * @method static mixed mergeBindings(self $query)
     * @method static mixed cleanBindings(array $bindings)
     * @method static mixed flattenValue($value)
     * @method static mixed defaultKeyName()
     * @method static mixed getConnection()
     * @method static mixed getProcessor()
     * @method static mixed getGrammar()
     * @method static mixed useWritePdo()
     * @method static mixed isQueryable($value)
     * @method static mixed clone()
     * @method static mixed cloneWithout(array $properties)
     * @method static mixed cloneWithoutBindings(array $except)
     * @method static mixed dump($args)
     * @method static mixed dumpRawSql()
     * @method static mixed dd()
     * @method static mixed ddRawSql()
     * @method static mixed wherePast($columns)
     * @method static mixed whereNowOrPast($columns)
     * @method static mixed orWherePast($columns)
     * @method static mixed orWhereNowOrPast($columns)
     * @method static mixed whereFuture($columns)
     * @method static mixed whereNowOrFuture($columns)
     * @method static mixed orWhereFuture($columns)
     * @method static mixed orWhereNowOrFuture($columns)
     * @method static mixed wherePastOrFuture($columns, $operator, $boolean)
     * @method static mixed whereToday($columns, $boolean)
     * @method static mixed whereBeforeToday($columns)
     * @method static mixed whereTodayOrBefore($columns)
     * @method static mixed whereAfterToday($columns)
     * @method static mixed whereTodayOrAfter($columns)
     * @method static mixed orWhereToday($columns)
     * @method static mixed orWhereBeforeToday($columns)
     * @method static mixed orWhereTodayOrBefore($columns)
     * @method static mixed orWhereAfterToday($columns)
     * @method static mixed orWhereTodayOrAfter($columns)
     * @method static mixed whereTodayBeforeOrAfter($columns, $operator, $boolean)
     * @method static mixed chunk($count, callable $callback)
     * @method static mixed chunkMap(callable $callback, $count)
     * @method static mixed each(callable $callback, $count)
     * @method static mixed chunkById($count, callable $callback, $column, $alias)
     * @method static mixed chunkByIdDesc($count, callable $callback, $column, $alias)
     * @method static mixed orderedChunkById($count, callable $callback, $column, $alias, $descending)
     * @method static mixed eachById(callable $callback, $count, $column, $alias)
     * @method static mixed lazy($chunkSize)
     * @method static mixed lazyById($chunkSize, $column, $alias)
     * @method static mixed lazyByIdDesc($chunkSize, $column, $alias)
     * @method static mixed orderedLazyById($chunkSize, $column, $alias, $descending)
     * @method static mixed first($columns)
     * @method static mixed firstOrFail($columns, $message)
     * @method static mixed sole($columns)
     * @method static mixed paginateUsingCursor($perPage, $columns, $cursorName, $cursor)
     * @method static mixed getOriginalColumnNameForCursorPagination($builder, string $parameter)
     * @method static mixed paginator($items, $total, $perPage, $currentPage, $options)
     * @method static mixed simplePaginator($items, $perPage, $currentPage, $options)
     * @method static mixed cursorPaginator($items, $perPage, $cursor, $options)
     * @method static mixed tap($callback)
     * @method static mixed when($value, callable $callback, callable $default)
     * @method static mixed unless($value, callable $callback, callable $default)
     * @method static mixed explain()
     * @method static mixed forwardCallTo($object, $method, $parameters)
     * @method static mixed forwardDecoratedCallTo($object, $method, $parameters)
     * @method static mixed throwBadMethodCallException($method)
     * @method static mixed macro($name, $macro)
     * @method static mixed mixin($mixin, $replace)
     * @method static mixed hasMacro($name)
     * @method static mixed flushMacros()
     * @method static mixed macroCall($method, $parameters)
     * @mixin \Illuminate\Database\Query\Builder
     */
    class Notification extends \Illuminate\Database\Eloquent\Model
    {
        //
    }

    /**
     * App\Models\Payment
     *
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property string|null $failure_reason
     * @property \Illuminate\Support\Carbon|null $processed_at
     * @property array|null $gateway_response
     * @property string|null $gateway
     * @property mixed $status
     * @property decimal:2 $amount
     * @property string|null $transaction_id
     * @property mixed $booking_id
     * @property int $id
     * @property-read mixed $status_color
     * @property-read \App\Models\Booking $booking
     * @method static \Illuminate\Database\Eloquent\Builder<Payment>|Payment whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Payment>|Payment whereBookingId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Payment>|Payment whereTransactionId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Payment>|Payment whereAmount($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Payment>|Payment whereStatus($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Payment>|Payment whereGateway($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Payment>|Payment whereGatewayResponse($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Payment>|Payment whereProcessedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Payment>|Payment whereFailureReason($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Payment>|Payment whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Payment>|Payment whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Payment>|Payment newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<Payment>|Payment newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<Payment>|Payment query()
     * @method static mixed select($columns)
     * @method static mixed selectSub($query, $as)
     * @method static mixed selectRaw($expression, array $bindings)
     * @method static mixed fromSub($query, $as)
     * @method static mixed fromRaw($expression, $bindings)
     * @method static mixed createSub($query)
     * @method static mixed parseSub($query)
     * @method static mixed prependDatabaseNameIfCrossDatabaseQuery($query)
     * @method static mixed addSelect($column)
     * @method static mixed distinct()
     * @method static mixed from($table, $as)
     * @method static mixed useIndex($index)
     * @method static mixed forceIndex($index)
     * @method static mixed ignoreIndex($index)
     * @method static mixed join($table, $first, $operator, $second, $type, $where)
     * @method static mixed joinWhere($table, $first, $operator, $second, $type)
     * @method static mixed joinSub($query, $as, $first, $operator, $second, $type, $where)
     * @method static mixed joinLateral($query, string $as, string $type)
     * @method static mixed leftJoinLateral($query, string $as)
     * @method static mixed leftJoin($table, $first, $operator, $second)
     * @method static mixed leftJoinWhere($table, $first, $operator, $second)
     * @method static mixed leftJoinSub($query, $as, $first, $operator, $second)
     * @method static mixed rightJoin($table, $first, $operator, $second)
     * @method static mixed rightJoinWhere($table, $first, $operator, $second)
     * @method static mixed rightJoinSub($query, $as, $first, $operator, $second)
     * @method static mixed crossJoin($table, $first, $operator, $second)
     * @method static mixed crossJoinSub($query, $as)
     * @method static mixed newJoinClause(self $parentQuery, $type, $table)
     * @method static mixed newJoinLateralClause(self $parentQuery, $type, $table)
     * @method static mixed mergeWheres($wheres, $bindings)
     * @method static mixed where($column, $operator, $value, $boolean)
     * @method static mixed addArrayOfWheres($column, $boolean, $method)
     * @method static mixed prepareValueAndOperator($value, $operator, $useDefault)
     * @method static mixed invalidOperatorAndValue($operator, $value)
     * @method static mixed invalidOperator($operator)
     * @method static mixed isBitwiseOperator($operator)
     * @method static mixed orWhere($column, $operator, $value)
     * @method static mixed whereNot($column, $operator, $value, $boolean)
     * @method static mixed orWhereNot($column, $operator, $value)
     * @method static mixed whereColumn($first, $operator, $second, $boolean)
     * @method static mixed orWhereColumn($first, $operator, $second)
     * @method static mixed whereRaw($sql, $bindings, $boolean)
     * @method static mixed orWhereRaw($sql, $bindings)
     * @method static mixed whereLike($column, $value, $caseSensitive, $boolean, $not)
     * @method static mixed orWhereLike($column, $value, $caseSensitive)
     * @method static mixed whereNotLike($column, $value, $caseSensitive, $boolean)
     * @method static mixed orWhereNotLike($column, $value, $caseSensitive)
     * @method static mixed whereIn($column, $values, $boolean, $not)
     * @method static mixed orWhereIn($column, $values)
     * @method static mixed whereNotIn($column, $values, $boolean)
     * @method static mixed orWhereNotIn($column, $values)
     * @method static mixed whereIntegerInRaw($column, $values, $boolean, $not)
     * @method static mixed orWhereIntegerInRaw($column, $values)
     * @method static mixed whereIntegerNotInRaw($column, $values, $boolean)
     * @method static mixed orWhereIntegerNotInRaw($column, $values)
     * @method static mixed whereNull($columns, $boolean, $not)
     * @method static mixed orWhereNull($column)
     * @method static mixed whereNotNull($columns, $boolean)
     * @method static mixed whereBetween($column, iterable $values, $boolean, $not)
     * @method static mixed whereBetweenColumns($column, array $values, $boolean, $not)
     * @method static mixed orWhereBetween($column, iterable $values)
     * @method static mixed orWhereBetweenColumns($column, array $values)
     * @method static mixed whereNotBetween($column, iterable $values, $boolean)
     * @method static mixed whereNotBetweenColumns($column, array $values, $boolean)
     * @method static mixed orWhereNotBetween($column, iterable $values)
     * @method static mixed orWhereNotBetweenColumns($column, array $values)
     * @method static mixed orWhereNotNull($column)
     * @method static mixed whereDate($column, $operator, $value, $boolean)
     * @method static mixed orWhereDate($column, $operator, $value)
     * @method static mixed whereTime($column, $operator, $value, $boolean)
     * @method static mixed orWhereTime($column, $operator, $value)
     * @method static mixed whereDay($column, $operator, $value, $boolean)
     * @method static mixed orWhereDay($column, $operator, $value)
     * @method static mixed whereMonth($column, $operator, $value, $boolean)
     * @method static mixed orWhereMonth($column, $operator, $value)
     * @method static mixed whereYear($column, $operator, $value, $boolean)
     * @method static mixed orWhereYear($column, $operator, $value)
     * @method static mixed addDateBasedWhere($type, $column, $operator, $value, $boolean)
     * @method static mixed whereNested(Closure $callback, $boolean)
     * @method static mixed forNestedWhere()
     * @method static mixed addNestedWhereQuery($query, $boolean)
     * @method static mixed whereSub($column, $operator, $callback, $boolean)
     * @method static mixed whereExists($callback, $boolean, $not)
     * @method static mixed orWhereExists($callback, $not)
     * @method static mixed whereNotExists($callback, $boolean)
     * @method static mixed orWhereNotExists($callback)
     * @method static mixed addWhereExistsQuery(self $query, $boolean, $not)
     * @method static mixed whereRowValues($columns, $operator, $values, $boolean)
     * @method static mixed orWhereRowValues($columns, $operator, $values)
     * @method static mixed whereJsonContains($column, $value, $boolean, $not)
     * @method static mixed orWhereJsonContains($column, $value)
     * @method static mixed whereJsonDoesntContain($column, $value, $boolean)
     * @method static mixed orWhereJsonDoesntContain($column, $value)
     * @method static mixed whereJsonOverlaps($column, $value, $boolean, $not)
     * @method static mixed orWhereJsonOverlaps($column, $value)
     * @method static mixed whereJsonDoesntOverlap($column, $value, $boolean)
     * @method static mixed orWhereJsonDoesntOverlap($column, $value)
     * @method static mixed whereJsonContainsKey($column, $boolean, $not)
     * @method static mixed orWhereJsonContainsKey($column)
     * @method static mixed whereJsonDoesntContainKey($column, $boolean)
     * @method static mixed orWhereJsonDoesntContainKey($column)
     * @method static mixed whereJsonLength($column, $operator, $value, $boolean)
     * @method static mixed orWhereJsonLength($column, $operator, $value)
     * @method static mixed dynamicWhere($method, $parameters)
     * @method static mixed addDynamic($segment, $connector, $parameters, $index)
     * @method static mixed whereFullText($columns, $value, array $options, $boolean)
     * @method static mixed orWhereFullText($columns, $value, array $options)
     * @method static mixed whereAll($columns, $operator, $value, $boolean)
     * @method static mixed orWhereAll($columns, $operator, $value)
     * @method static mixed whereAny($columns, $operator, $value, $boolean)
     * @method static mixed orWhereAny($columns, $operator, $value)
     * @method static mixed whereNone($columns, $operator, $value, $boolean)
     * @method static mixed orWhereNone($columns, $operator, $value)
     * @method static mixed groupBy($groups)
     * @method static mixed groupByRaw($sql, array $bindings)
     * @method static mixed having($column, $operator, $value, $boolean)
     * @method static mixed orHaving($column, $operator, $value)
     * @method static mixed havingNested(Closure $callback, $boolean)
     * @method static mixed addNestedHavingQuery($query, $boolean)
     * @method static mixed havingNull($columns, $boolean, $not)
     * @method static mixed orHavingNull($column)
     * @method static mixed havingNotNull($columns, $boolean)
     * @method static mixed orHavingNotNull($column)
     * @method static mixed havingBetween($column, iterable $values, $boolean, $not)
     * @method static mixed havingRaw($sql, array $bindings, $boolean)
     * @method static mixed orHavingRaw($sql, array $bindings)
     * @method static mixed orderBy($column, $direction)
     * @method static mixed orderByDesc($column)
     * @method static mixed latest($column)
     * @method static mixed oldest($column)
     * @method static mixed inRandomOrder($seed)
     * @method static mixed orderByRaw($sql, $bindings)
     * @method static mixed skip($value)
     * @method static mixed offset($value)
     * @method static mixed take($value)
     * @method static mixed limit($value)
     * @method static mixed groupLimit($value, $column)
     * @method static mixed forPage($page, $perPage)
     * @method static mixed forPageBeforeId($perPage, $lastId, $column)
     * @method static mixed forPageAfterId($perPage, $lastId, $column)
     * @method static mixed reorder($column, $direction)
     * @method static mixed removeExistingOrdersFor($column)
     * @method static mixed union($query, $all)
     * @method static mixed unionAll($query)
     * @method static mixed lock($value)
     * @method static mixed lockForUpdate()
     * @method static mixed sharedLock()
     * @method static mixed beforeQuery(callable $callback)
     * @method static mixed applyBeforeQueryCallbacks()
     * @method static mixed afterQuery(Closure $callback)
     * @method static mixed applyAfterQueryCallbacks($result)
     * @method static mixed toSql()
     * @method static mixed toRawSql()
     * @method static mixed find($id, $columns)
     * @method static mixed findOr($id, $columns, Closure $callback)
     * @method static mixed value($column)
     * @method static mixed rawValue(string $expression, array $bindings)
     * @method static mixed soleValue($column)
     * @method static mixed get($columns)
     * @method static mixed runSelect()
     * @method static mixed withoutGroupLimitKeys($items)
     * @method static mixed paginate($perPage, $columns, $pageName, $page, $total)
     * @method static mixed simplePaginate($perPage, $columns, $pageName, $page)
     * @method static mixed cursorPaginate($perPage, $columns, $cursorName, $cursor)
     * @method static mixed ensureOrderForCursorPagination($shouldReverse)
     * @method static mixed getCountForPagination($columns)
     * @method static mixed runPaginationCountQuery($columns)
     * @method static mixed cloneForPaginationCount()
     * @method static mixed withoutSelectAliases(array $columns)
     * @method static mixed cursor()
     * @method static mixed enforceOrderBy()
     * @method static mixed pluck($column, $key)
     * @method static mixed stripTableForPluck($column)
     * @method static mixed pluckFromObjectColumn($queryResult, $column, $key)
     * @method static mixed pluckFromArrayColumn($queryResult, $column, $key)
     * @method static mixed implode($column, $glue)
     * @method static mixed exists()
     * @method static mixed doesntExist()
     * @method static mixed existsOr(Closure $callback)
     * @method static mixed doesntExistOr(Closure $callback)
     * @method static mixed count($columns)
     * @method static mixed min($column)
     * @method static mixed max($column)
     * @method static mixed sum($column)
     * @method static mixed avg($column)
     * @method static mixed average($column)
     * @method static mixed aggregate($function, $columns)
     * @method static mixed numericAggregate($function, $columns)
     * @method static mixed setAggregate($function, $columns)
     * @method static mixed onceWithColumns($columns, $callback)
     * @method static mixed insert(array $values)
     * @method static mixed insertOrIgnore(array $values)
     * @method static mixed insertGetId(array $values, $sequence)
     * @method static mixed insertUsing(array $columns, $query)
     * @method static mixed insertOrIgnoreUsing(array $columns, $query)
     * @method static mixed update(array $values)
     * @method static mixed updateFrom(array $values)
     * @method static mixed updateOrInsert(array $attributes, callable|array $values)
     * @method static mixed upsert(array $values, $uniqueBy, $update)
     * @method static mixed increment($column, $amount, array $extra)
     * @method static mixed incrementEach(array $columns, array $extra)
     * @method static mixed decrement($column, $amount, array $extra)
     * @method static mixed decrementEach(array $columns, array $extra)
     * @method static mixed delete($id)
     * @method static mixed truncate()
     * @method static mixed newQuery()
     * @method static mixed forSubQuery()
     * @method static mixed getColumns()
     * @method static mixed raw($value)
     * @method static mixed getUnionBuilders()
     * @method static mixed getBindings()
     * @method static mixed getRawBindings()
     * @method static mixed setBindings(array $bindings, $type)
     * @method static mixed addBinding($value, $type)
     * @method static mixed castBinding($value)
     * @method static mixed mergeBindings(self $query)
     * @method static mixed cleanBindings(array $bindings)
     * @method static mixed flattenValue($value)
     * @method static mixed defaultKeyName()
     * @method static mixed getConnection()
     * @method static mixed getProcessor()
     * @method static mixed getGrammar()
     * @method static mixed useWritePdo()
     * @method static mixed isQueryable($value)
     * @method static mixed clone()
     * @method static mixed cloneWithout(array $properties)
     * @method static mixed cloneWithoutBindings(array $except)
     * @method static mixed dump($args)
     * @method static mixed dumpRawSql()
     * @method static mixed dd()
     * @method static mixed ddRawSql()
     * @method static mixed wherePast($columns)
     * @method static mixed whereNowOrPast($columns)
     * @method static mixed orWherePast($columns)
     * @method static mixed orWhereNowOrPast($columns)
     * @method static mixed whereFuture($columns)
     * @method static mixed whereNowOrFuture($columns)
     * @method static mixed orWhereFuture($columns)
     * @method static mixed orWhereNowOrFuture($columns)
     * @method static mixed wherePastOrFuture($columns, $operator, $boolean)
     * @method static mixed whereToday($columns, $boolean)
     * @method static mixed whereBeforeToday($columns)
     * @method static mixed whereTodayOrBefore($columns)
     * @method static mixed whereAfterToday($columns)
     * @method static mixed whereTodayOrAfter($columns)
     * @method static mixed orWhereToday($columns)
     * @method static mixed orWhereBeforeToday($columns)
     * @method static mixed orWhereTodayOrBefore($columns)
     * @method static mixed orWhereAfterToday($columns)
     * @method static mixed orWhereTodayOrAfter($columns)
     * @method static mixed whereTodayBeforeOrAfter($columns, $operator, $boolean)
     * @method static mixed chunk($count, callable $callback)
     * @method static mixed chunkMap(callable $callback, $count)
     * @method static mixed each(callable $callback, $count)
     * @method static mixed chunkById($count, callable $callback, $column, $alias)
     * @method static mixed chunkByIdDesc($count, callable $callback, $column, $alias)
     * @method static mixed orderedChunkById($count, callable $callback, $column, $alias, $descending)
     * @method static mixed eachById(callable $callback, $count, $column, $alias)
     * @method static mixed lazy($chunkSize)
     * @method static mixed lazyById($chunkSize, $column, $alias)
     * @method static mixed lazyByIdDesc($chunkSize, $column, $alias)
     * @method static mixed orderedLazyById($chunkSize, $column, $alias, $descending)
     * @method static mixed first($columns)
     * @method static mixed firstOrFail($columns, $message)
     * @method static mixed sole($columns)
     * @method static mixed paginateUsingCursor($perPage, $columns, $cursorName, $cursor)
     * @method static mixed getOriginalColumnNameForCursorPagination($builder, string $parameter)
     * @method static mixed paginator($items, $total, $perPage, $currentPage, $options)
     * @method static mixed simplePaginator($items, $perPage, $currentPage, $options)
     * @method static mixed cursorPaginator($items, $perPage, $cursor, $options)
     * @method static mixed tap($callback)
     * @method static mixed when($value, callable $callback, callable $default)
     * @method static mixed unless($value, callable $callback, callable $default)
     * @method static mixed explain()
     * @method static mixed forwardCallTo($object, $method, $parameters)
     * @method static mixed forwardDecoratedCallTo($object, $method, $parameters)
     * @method static mixed throwBadMethodCallException($method)
     * @method static mixed macro($name, $macro)
     * @method static mixed mixin($mixin, $replace)
     * @method static mixed hasMacro($name)
     * @method static mixed flushMacros()
     * @method static mixed macroCall($method, $parameters)
     * @mixin \Illuminate\Database\Query\Builder
     */
    class Payment extends \Illuminate\Database\Eloquent\Model
    {
        //
    }

    /**
     * App\Models\Review
     *
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property string|null $comment
     * @property integer $rating
     * @property mixed $user_id
     * @property mixed $booking_id
     * @property int $id
     * @property-read mixed $stars
     * @property-read \App\Models\Booking $booking
     * @property-read \App\Models\User $customer
     * @method static \Illuminate\Database\Eloquent\Builder<Review>|Review whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Review>|Review whereBookingId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Review>|Review whereUserId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Review>|Review whereRating($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Review>|Review whereComment($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Review>|Review whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Review>|Review whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Review>|Review withRating()
     * @method static \Illuminate\Database\Eloquent\Builder<Review>|Review recent()
     * @method static \Illuminate\Database\Eloquent\Builder<Review>|Review newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<Review>|Review newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<Review>|Review query()
     * @method static mixed select($columns)
     * @method static mixed selectSub($query, $as)
     * @method static mixed selectRaw($expression, array $bindings)
     * @method static mixed fromSub($query, $as)
     * @method static mixed fromRaw($expression, $bindings)
     * @method static mixed createSub($query)
     * @method static mixed parseSub($query)
     * @method static mixed prependDatabaseNameIfCrossDatabaseQuery($query)
     * @method static mixed addSelect($column)
     * @method static mixed distinct()
     * @method static mixed from($table, $as)
     * @method static mixed useIndex($index)
     * @method static mixed forceIndex($index)
     * @method static mixed ignoreIndex($index)
     * @method static mixed join($table, $first, $operator, $second, $type, $where)
     * @method static mixed joinWhere($table, $first, $operator, $second, $type)
     * @method static mixed joinSub($query, $as, $first, $operator, $second, $type, $where)
     * @method static mixed joinLateral($query, string $as, string $type)
     * @method static mixed leftJoinLateral($query, string $as)
     * @method static mixed leftJoin($table, $first, $operator, $second)
     * @method static mixed leftJoinWhere($table, $first, $operator, $second)
     * @method static mixed leftJoinSub($query, $as, $first, $operator, $second)
     * @method static mixed rightJoin($table, $first, $operator, $second)
     * @method static mixed rightJoinWhere($table, $first, $operator, $second)
     * @method static mixed rightJoinSub($query, $as, $first, $operator, $second)
     * @method static mixed crossJoin($table, $first, $operator, $second)
     * @method static mixed crossJoinSub($query, $as)
     * @method static mixed newJoinClause(self $parentQuery, $type, $table)
     * @method static mixed newJoinLateralClause(self $parentQuery, $type, $table)
     * @method static mixed mergeWheres($wheres, $bindings)
     * @method static mixed where($column, $operator, $value, $boolean)
     * @method static mixed addArrayOfWheres($column, $boolean, $method)
     * @method static mixed prepareValueAndOperator($value, $operator, $useDefault)
     * @method static mixed invalidOperatorAndValue($operator, $value)
     * @method static mixed invalidOperator($operator)
     * @method static mixed isBitwiseOperator($operator)
     * @method static mixed orWhere($column, $operator, $value)
     * @method static mixed whereNot($column, $operator, $value, $boolean)
     * @method static mixed orWhereNot($column, $operator, $value)
     * @method static mixed whereColumn($first, $operator, $second, $boolean)
     * @method static mixed orWhereColumn($first, $operator, $second)
     * @method static mixed whereRaw($sql, $bindings, $boolean)
     * @method static mixed orWhereRaw($sql, $bindings)
     * @method static mixed whereLike($column, $value, $caseSensitive, $boolean, $not)
     * @method static mixed orWhereLike($column, $value, $caseSensitive)
     * @method static mixed whereNotLike($column, $value, $caseSensitive, $boolean)
     * @method static mixed orWhereNotLike($column, $value, $caseSensitive)
     * @method static mixed whereIn($column, $values, $boolean, $not)
     * @method static mixed orWhereIn($column, $values)
     * @method static mixed whereNotIn($column, $values, $boolean)
     * @method static mixed orWhereNotIn($column, $values)
     * @method static mixed whereIntegerInRaw($column, $values, $boolean, $not)
     * @method static mixed orWhereIntegerInRaw($column, $values)
     * @method static mixed whereIntegerNotInRaw($column, $values, $boolean)
     * @method static mixed orWhereIntegerNotInRaw($column, $values)
     * @method static mixed whereNull($columns, $boolean, $not)
     * @method static mixed orWhereNull($column)
     * @method static mixed whereNotNull($columns, $boolean)
     * @method static mixed whereBetween($column, iterable $values, $boolean, $not)
     * @method static mixed whereBetweenColumns($column, array $values, $boolean, $not)
     * @method static mixed orWhereBetween($column, iterable $values)
     * @method static mixed orWhereBetweenColumns($column, array $values)
     * @method static mixed whereNotBetween($column, iterable $values, $boolean)
     * @method static mixed whereNotBetweenColumns($column, array $values, $boolean)
     * @method static mixed orWhereNotBetween($column, iterable $values)
     * @method static mixed orWhereNotBetweenColumns($column, array $values)
     * @method static mixed orWhereNotNull($column)
     * @method static mixed whereDate($column, $operator, $value, $boolean)
     * @method static mixed orWhereDate($column, $operator, $value)
     * @method static mixed whereTime($column, $operator, $value, $boolean)
     * @method static mixed orWhereTime($column, $operator, $value)
     * @method static mixed whereDay($column, $operator, $value, $boolean)
     * @method static mixed orWhereDay($column, $operator, $value)
     * @method static mixed whereMonth($column, $operator, $value, $boolean)
     * @method static mixed orWhereMonth($column, $operator, $value)
     * @method static mixed whereYear($column, $operator, $value, $boolean)
     * @method static mixed orWhereYear($column, $operator, $value)
     * @method static mixed addDateBasedWhere($type, $column, $operator, $value, $boolean)
     * @method static mixed whereNested(Closure $callback, $boolean)
     * @method static mixed forNestedWhere()
     * @method static mixed addNestedWhereQuery($query, $boolean)
     * @method static mixed whereSub($column, $operator, $callback, $boolean)
     * @method static mixed whereExists($callback, $boolean, $not)
     * @method static mixed orWhereExists($callback, $not)
     * @method static mixed whereNotExists($callback, $boolean)
     * @method static mixed orWhereNotExists($callback)
     * @method static mixed addWhereExistsQuery(self $query, $boolean, $not)
     * @method static mixed whereRowValues($columns, $operator, $values, $boolean)
     * @method static mixed orWhereRowValues($columns, $operator, $values)
     * @method static mixed whereJsonContains($column, $value, $boolean, $not)
     * @method static mixed orWhereJsonContains($column, $value)
     * @method static mixed whereJsonDoesntContain($column, $value, $boolean)
     * @method static mixed orWhereJsonDoesntContain($column, $value)
     * @method static mixed whereJsonOverlaps($column, $value, $boolean, $not)
     * @method static mixed orWhereJsonOverlaps($column, $value)
     * @method static mixed whereJsonDoesntOverlap($column, $value, $boolean)
     * @method static mixed orWhereJsonDoesntOverlap($column, $value)
     * @method static mixed whereJsonContainsKey($column, $boolean, $not)
     * @method static mixed orWhereJsonContainsKey($column)
     * @method static mixed whereJsonDoesntContainKey($column, $boolean)
     * @method static mixed orWhereJsonDoesntContainKey($column)
     * @method static mixed whereJsonLength($column, $operator, $value, $boolean)
     * @method static mixed orWhereJsonLength($column, $operator, $value)
     * @method static mixed dynamicWhere($method, $parameters)
     * @method static mixed addDynamic($segment, $connector, $parameters, $index)
     * @method static mixed whereFullText($columns, $value, array $options, $boolean)
     * @method static mixed orWhereFullText($columns, $value, array $options)
     * @method static mixed whereAll($columns, $operator, $value, $boolean)
     * @method static mixed orWhereAll($columns, $operator, $value)
     * @method static mixed whereAny($columns, $operator, $value, $boolean)
     * @method static mixed orWhereAny($columns, $operator, $value)
     * @method static mixed whereNone($columns, $operator, $value, $boolean)
     * @method static mixed orWhereNone($columns, $operator, $value)
     * @method static mixed groupBy($groups)
     * @method static mixed groupByRaw($sql, array $bindings)
     * @method static mixed having($column, $operator, $value, $boolean)
     * @method static mixed orHaving($column, $operator, $value)
     * @method static mixed havingNested(Closure $callback, $boolean)
     * @method static mixed addNestedHavingQuery($query, $boolean)
     * @method static mixed havingNull($columns, $boolean, $not)
     * @method static mixed orHavingNull($column)
     * @method static mixed havingNotNull($columns, $boolean)
     * @method static mixed orHavingNotNull($column)
     * @method static mixed havingBetween($column, iterable $values, $boolean, $not)
     * @method static mixed havingRaw($sql, array $bindings, $boolean)
     * @method static mixed orHavingRaw($sql, array $bindings)
     * @method static mixed orderBy($column, $direction)
     * @method static mixed orderByDesc($column)
     * @method static mixed latest($column)
     * @method static mixed oldest($column)
     * @method static mixed inRandomOrder($seed)
     * @method static mixed orderByRaw($sql, $bindings)
     * @method static mixed skip($value)
     * @method static mixed offset($value)
     * @method static mixed take($value)
     * @method static mixed limit($value)
     * @method static mixed groupLimit($value, $column)
     * @method static mixed forPage($page, $perPage)
     * @method static mixed forPageBeforeId($perPage, $lastId, $column)
     * @method static mixed forPageAfterId($perPage, $lastId, $column)
     * @method static mixed reorder($column, $direction)
     * @method static mixed removeExistingOrdersFor($column)
     * @method static mixed union($query, $all)
     * @method static mixed unionAll($query)
     * @method static mixed lock($value)
     * @method static mixed lockForUpdate()
     * @method static mixed sharedLock()
     * @method static mixed beforeQuery(callable $callback)
     * @method static mixed applyBeforeQueryCallbacks()
     * @method static mixed afterQuery(Closure $callback)
     * @method static mixed applyAfterQueryCallbacks($result)
     * @method static mixed toSql()
     * @method static mixed toRawSql()
     * @method static mixed find($id, $columns)
     * @method static mixed findOr($id, $columns, Closure $callback)
     * @method static mixed value($column)
     * @method static mixed rawValue(string $expression, array $bindings)
     * @method static mixed soleValue($column)
     * @method static mixed get($columns)
     * @method static mixed runSelect()
     * @method static mixed withoutGroupLimitKeys($items)
     * @method static mixed paginate($perPage, $columns, $pageName, $page, $total)
     * @method static mixed simplePaginate($perPage, $columns, $pageName, $page)
     * @method static mixed cursorPaginate($perPage, $columns, $cursorName, $cursor)
     * @method static mixed ensureOrderForCursorPagination($shouldReverse)
     * @method static mixed getCountForPagination($columns)
     * @method static mixed runPaginationCountQuery($columns)
     * @method static mixed cloneForPaginationCount()
     * @method static mixed withoutSelectAliases(array $columns)
     * @method static mixed cursor()
     * @method static mixed enforceOrderBy()
     * @method static mixed pluck($column, $key)
     * @method static mixed stripTableForPluck($column)
     * @method static mixed pluckFromObjectColumn($queryResult, $column, $key)
     * @method static mixed pluckFromArrayColumn($queryResult, $column, $key)
     * @method static mixed implode($column, $glue)
     * @method static mixed exists()
     * @method static mixed doesntExist()
     * @method static mixed existsOr(Closure $callback)
     * @method static mixed doesntExistOr(Closure $callback)
     * @method static mixed count($columns)
     * @method static mixed min($column)
     * @method static mixed max($column)
     * @method static mixed sum($column)
     * @method static mixed avg($column)
     * @method static mixed average($column)
     * @method static mixed aggregate($function, $columns)
     * @method static mixed numericAggregate($function, $columns)
     * @method static mixed setAggregate($function, $columns)
     * @method static mixed onceWithColumns($columns, $callback)
     * @method static mixed insert(array $values)
     * @method static mixed insertOrIgnore(array $values)
     * @method static mixed insertGetId(array $values, $sequence)
     * @method static mixed insertUsing(array $columns, $query)
     * @method static mixed insertOrIgnoreUsing(array $columns, $query)
     * @method static mixed update(array $values)
     * @method static mixed updateFrom(array $values)
     * @method static mixed updateOrInsert(array $attributes, callable|array $values)
     * @method static mixed upsert(array $values, $uniqueBy, $update)
     * @method static mixed increment($column, $amount, array $extra)
     * @method static mixed incrementEach(array $columns, array $extra)
     * @method static mixed decrement($column, $amount, array $extra)
     * @method static mixed decrementEach(array $columns, array $extra)
     * @method static mixed delete($id)
     * @method static mixed truncate()
     * @method static mixed newQuery()
     * @method static mixed forSubQuery()
     * @method static mixed getColumns()
     * @method static mixed raw($value)
     * @method static mixed getUnionBuilders()
     * @method static mixed getBindings()
     * @method static mixed getRawBindings()
     * @method static mixed setBindings(array $bindings, $type)
     * @method static mixed addBinding($value, $type)
     * @method static mixed castBinding($value)
     * @method static mixed mergeBindings(self $query)
     * @method static mixed cleanBindings(array $bindings)
     * @method static mixed flattenValue($value)
     * @method static mixed defaultKeyName()
     * @method static mixed getConnection()
     * @method static mixed getProcessor()
     * @method static mixed getGrammar()
     * @method static mixed useWritePdo()
     * @method static mixed isQueryable($value)
     * @method static mixed clone()
     * @method static mixed cloneWithout(array $properties)
     * @method static mixed cloneWithoutBindings(array $except)
     * @method static mixed dump($args)
     * @method static mixed dumpRawSql()
     * @method static mixed dd()
     * @method static mixed ddRawSql()
     * @method static mixed wherePast($columns)
     * @method static mixed whereNowOrPast($columns)
     * @method static mixed orWherePast($columns)
     * @method static mixed orWhereNowOrPast($columns)
     * @method static mixed whereFuture($columns)
     * @method static mixed whereNowOrFuture($columns)
     * @method static mixed orWhereFuture($columns)
     * @method static mixed orWhereNowOrFuture($columns)
     * @method static mixed wherePastOrFuture($columns, $operator, $boolean)
     * @method static mixed whereToday($columns, $boolean)
     * @method static mixed whereBeforeToday($columns)
     * @method static mixed whereTodayOrBefore($columns)
     * @method static mixed whereAfterToday($columns)
     * @method static mixed whereTodayOrAfter($columns)
     * @method static mixed orWhereToday($columns)
     * @method static mixed orWhereBeforeToday($columns)
     * @method static mixed orWhereTodayOrBefore($columns)
     * @method static mixed orWhereAfterToday($columns)
     * @method static mixed orWhereTodayOrAfter($columns)
     * @method static mixed whereTodayBeforeOrAfter($columns, $operator, $boolean)
     * @method static mixed chunk($count, callable $callback)
     * @method static mixed chunkMap(callable $callback, $count)
     * @method static mixed each(callable $callback, $count)
     * @method static mixed chunkById($count, callable $callback, $column, $alias)
     * @method static mixed chunkByIdDesc($count, callable $callback, $column, $alias)
     * @method static mixed orderedChunkById($count, callable $callback, $column, $alias, $descending)
     * @method static mixed eachById(callable $callback, $count, $column, $alias)
     * @method static mixed lazy($chunkSize)
     * @method static mixed lazyById($chunkSize, $column, $alias)
     * @method static mixed lazyByIdDesc($chunkSize, $column, $alias)
     * @method static mixed orderedLazyById($chunkSize, $column, $alias, $descending)
     * @method static mixed first($columns)
     * @method static mixed firstOrFail($columns, $message)
     * @method static mixed sole($columns)
     * @method static mixed paginateUsingCursor($perPage, $columns, $cursorName, $cursor)
     * @method static mixed getOriginalColumnNameForCursorPagination($builder, string $parameter)
     * @method static mixed paginator($items, $total, $perPage, $currentPage, $options)
     * @method static mixed simplePaginator($items, $perPage, $currentPage, $options)
     * @method static mixed cursorPaginator($items, $perPage, $cursor, $options)
     * @method static mixed tap($callback)
     * @method static mixed when($value, callable $callback, callable $default)
     * @method static mixed unless($value, callable $callback, callable $default)
     * @method static mixed explain()
     * @method static mixed forwardCallTo($object, $method, $parameters)
     * @method static mixed forwardDecoratedCallTo($object, $method, $parameters)
     * @method static mixed throwBadMethodCallException($method)
     * @method static mixed macro($name, $macro)
     * @method static mixed mixin($mixin, $replace)
     * @method static mixed hasMacro($name)
     * @method static mixed flushMacros()
     * @method static mixed macroCall($method, $parameters)
     * @mixin \Illuminate\Database\Query\Builder
     */
    class Review extends \Illuminate\Database\Eloquent\Model
    {
        //
    }

    /**
     * App\Models\Setting
     *
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property boolean $is_public
     * @property string|null $description
     * @property string $label
     * @property string $group
     * @property string $type
     * @property string|null $value
     * @property string $key
     * @property int $id
     * @method static \Illuminate\Database\Eloquent\Builder<Setting>|Setting whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Setting>|Setting whereKey($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Setting>|Setting whereValue($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Setting>|Setting whereType($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Setting>|Setting whereGroup($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Setting>|Setting whereLabel($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Setting>|Setting whereDescription($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Setting>|Setting whereIsPublic($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Setting>|Setting whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Setting>|Setting whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Setting>|Setting newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<Setting>|Setting newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<Setting>|Setting query()
     * @method static mixed select($columns)
     * @method static mixed selectSub($query, $as)
     * @method static mixed selectRaw($expression, array $bindings)
     * @method static mixed fromSub($query, $as)
     * @method static mixed fromRaw($expression, $bindings)
     * @method static mixed createSub($query)
     * @method static mixed parseSub($query)
     * @method static mixed prependDatabaseNameIfCrossDatabaseQuery($query)
     * @method static mixed addSelect($column)
     * @method static mixed distinct()
     * @method static mixed from($table, $as)
     * @method static mixed useIndex($index)
     * @method static mixed forceIndex($index)
     * @method static mixed ignoreIndex($index)
     * @method static mixed join($table, $first, $operator, $second, $type, $where)
     * @method static mixed joinWhere($table, $first, $operator, $second, $type)
     * @method static mixed joinSub($query, $as, $first, $operator, $second, $type, $where)
     * @method static mixed joinLateral($query, string $as, string $type)
     * @method static mixed leftJoinLateral($query, string $as)
     * @method static mixed leftJoin($table, $first, $operator, $second)
     * @method static mixed leftJoinWhere($table, $first, $operator, $second)
     * @method static mixed leftJoinSub($query, $as, $first, $operator, $second)
     * @method static mixed rightJoin($table, $first, $operator, $second)
     * @method static mixed rightJoinWhere($table, $first, $operator, $second)
     * @method static mixed rightJoinSub($query, $as, $first, $operator, $second)
     * @method static mixed crossJoin($table, $first, $operator, $second)
     * @method static mixed crossJoinSub($query, $as)
     * @method static mixed newJoinClause(self $parentQuery, $type, $table)
     * @method static mixed newJoinLateralClause(self $parentQuery, $type, $table)
     * @method static mixed mergeWheres($wheres, $bindings)
     * @method static mixed where($column, $operator, $value, $boolean)
     * @method static mixed addArrayOfWheres($column, $boolean, $method)
     * @method static mixed prepareValueAndOperator($value, $operator, $useDefault)
     * @method static mixed invalidOperatorAndValue($operator, $value)
     * @method static mixed invalidOperator($operator)
     * @method static mixed isBitwiseOperator($operator)
     * @method static mixed orWhere($column, $operator, $value)
     * @method static mixed whereNot($column, $operator, $value, $boolean)
     * @method static mixed orWhereNot($column, $operator, $value)
     * @method static mixed whereColumn($first, $operator, $second, $boolean)
     * @method static mixed orWhereColumn($first, $operator, $second)
     * @method static mixed whereRaw($sql, $bindings, $boolean)
     * @method static mixed orWhereRaw($sql, $bindings)
     * @method static mixed whereLike($column, $value, $caseSensitive, $boolean, $not)
     * @method static mixed orWhereLike($column, $value, $caseSensitive)
     * @method static mixed whereNotLike($column, $value, $caseSensitive, $boolean)
     * @method static mixed orWhereNotLike($column, $value, $caseSensitive)
     * @method static mixed whereIn($column, $values, $boolean, $not)
     * @method static mixed orWhereIn($column, $values)
     * @method static mixed whereNotIn($column, $values, $boolean)
     * @method static mixed orWhereNotIn($column, $values)
     * @method static mixed whereIntegerInRaw($column, $values, $boolean, $not)
     * @method static mixed orWhereIntegerInRaw($column, $values)
     * @method static mixed whereIntegerNotInRaw($column, $values, $boolean)
     * @method static mixed orWhereIntegerNotInRaw($column, $values)
     * @method static mixed whereNull($columns, $boolean, $not)
     * @method static mixed orWhereNull($column)
     * @method static mixed whereNotNull($columns, $boolean)
     * @method static mixed whereBetween($column, iterable $values, $boolean, $not)
     * @method static mixed whereBetweenColumns($column, array $values, $boolean, $not)
     * @method static mixed orWhereBetween($column, iterable $values)
     * @method static mixed orWhereBetweenColumns($column, array $values)
     * @method static mixed whereNotBetween($column, iterable $values, $boolean)
     * @method static mixed whereNotBetweenColumns($column, array $values, $boolean)
     * @method static mixed orWhereNotBetween($column, iterable $values)
     * @method static mixed orWhereNotBetweenColumns($column, array $values)
     * @method static mixed orWhereNotNull($column)
     * @method static mixed whereDate($column, $operator, $value, $boolean)
     * @method static mixed orWhereDate($column, $operator, $value)
     * @method static mixed whereTime($column, $operator, $value, $boolean)
     * @method static mixed orWhereTime($column, $operator, $value)
     * @method static mixed whereDay($column, $operator, $value, $boolean)
     * @method static mixed orWhereDay($column, $operator, $value)
     * @method static mixed whereMonth($column, $operator, $value, $boolean)
     * @method static mixed orWhereMonth($column, $operator, $value)
     * @method static mixed whereYear($column, $operator, $value, $boolean)
     * @method static mixed orWhereYear($column, $operator, $value)
     * @method static mixed addDateBasedWhere($type, $column, $operator, $value, $boolean)
     * @method static mixed whereNested(Closure $callback, $boolean)
     * @method static mixed forNestedWhere()
     * @method static mixed addNestedWhereQuery($query, $boolean)
     * @method static mixed whereSub($column, $operator, $callback, $boolean)
     * @method static mixed whereExists($callback, $boolean, $not)
     * @method static mixed orWhereExists($callback, $not)
     * @method static mixed whereNotExists($callback, $boolean)
     * @method static mixed orWhereNotExists($callback)
     * @method static mixed addWhereExistsQuery(self $query, $boolean, $not)
     * @method static mixed whereRowValues($columns, $operator, $values, $boolean)
     * @method static mixed orWhereRowValues($columns, $operator, $values)
     * @method static mixed whereJsonContains($column, $value, $boolean, $not)
     * @method static mixed orWhereJsonContains($column, $value)
     * @method static mixed whereJsonDoesntContain($column, $value, $boolean)
     * @method static mixed orWhereJsonDoesntContain($column, $value)
     * @method static mixed whereJsonOverlaps($column, $value, $boolean, $not)
     * @method static mixed orWhereJsonOverlaps($column, $value)
     * @method static mixed whereJsonDoesntOverlap($column, $value, $boolean)
     * @method static mixed orWhereJsonDoesntOverlap($column, $value)
     * @method static mixed whereJsonContainsKey($column, $boolean, $not)
     * @method static mixed orWhereJsonContainsKey($column)
     * @method static mixed whereJsonDoesntContainKey($column, $boolean)
     * @method static mixed orWhereJsonDoesntContainKey($column)
     * @method static mixed whereJsonLength($column, $operator, $value, $boolean)
     * @method static mixed orWhereJsonLength($column, $operator, $value)
     * @method static mixed dynamicWhere($method, $parameters)
     * @method static mixed addDynamic($segment, $connector, $parameters, $index)
     * @method static mixed whereFullText($columns, $value, array $options, $boolean)
     * @method static mixed orWhereFullText($columns, $value, array $options)
     * @method static mixed whereAll($columns, $operator, $value, $boolean)
     * @method static mixed orWhereAll($columns, $operator, $value)
     * @method static mixed whereAny($columns, $operator, $value, $boolean)
     * @method static mixed orWhereAny($columns, $operator, $value)
     * @method static mixed whereNone($columns, $operator, $value, $boolean)
     * @method static mixed orWhereNone($columns, $operator, $value)
     * @method static mixed groupBy($groups)
     * @method static mixed groupByRaw($sql, array $bindings)
     * @method static mixed having($column, $operator, $value, $boolean)
     * @method static mixed orHaving($column, $operator, $value)
     * @method static mixed havingNested(Closure $callback, $boolean)
     * @method static mixed addNestedHavingQuery($query, $boolean)
     * @method static mixed havingNull($columns, $boolean, $not)
     * @method static mixed orHavingNull($column)
     * @method static mixed havingNotNull($columns, $boolean)
     * @method static mixed orHavingNotNull($column)
     * @method static mixed havingBetween($column, iterable $values, $boolean, $not)
     * @method static mixed havingRaw($sql, array $bindings, $boolean)
     * @method static mixed orHavingRaw($sql, array $bindings)
     * @method static mixed orderBy($column, $direction)
     * @method static mixed orderByDesc($column)
     * @method static mixed latest($column)
     * @method static mixed oldest($column)
     * @method static mixed inRandomOrder($seed)
     * @method static mixed orderByRaw($sql, $bindings)
     * @method static mixed skip($value)
     * @method static mixed offset($value)
     * @method static mixed take($value)
     * @method static mixed limit($value)
     * @method static mixed groupLimit($value, $column)
     * @method static mixed forPage($page, $perPage)
     * @method static mixed forPageBeforeId($perPage, $lastId, $column)
     * @method static mixed forPageAfterId($perPage, $lastId, $column)
     * @method static mixed reorder($column, $direction)
     * @method static mixed removeExistingOrdersFor($column)
     * @method static mixed union($query, $all)
     * @method static mixed unionAll($query)
     * @method static mixed lock($value)
     * @method static mixed lockForUpdate()
     * @method static mixed sharedLock()
     * @method static mixed beforeQuery(callable $callback)
     * @method static mixed applyBeforeQueryCallbacks()
     * @method static mixed afterQuery(Closure $callback)
     * @method static mixed applyAfterQueryCallbacks($result)
     * @method static mixed toSql()
     * @method static mixed toRawSql()
     * @method static mixed find($id, $columns)
     * @method static mixed findOr($id, $columns, Closure $callback)
     * @method static mixed value($column)
     * @method static mixed rawValue(string $expression, array $bindings)
     * @method static mixed soleValue($column)
     * @method static mixed get($columns)
     * @method static mixed runSelect()
     * @method static mixed withoutGroupLimitKeys($items)
     * @method static mixed paginate($perPage, $columns, $pageName, $page, $total)
     * @method static mixed simplePaginate($perPage, $columns, $pageName, $page)
     * @method static mixed cursorPaginate($perPage, $columns, $cursorName, $cursor)
     * @method static mixed ensureOrderForCursorPagination($shouldReverse)
     * @method static mixed getCountForPagination($columns)
     * @method static mixed runPaginationCountQuery($columns)
     * @method static mixed cloneForPaginationCount()
     * @method static mixed withoutSelectAliases(array $columns)
     * @method static mixed cursor()
     * @method static mixed enforceOrderBy()
     * @method static mixed pluck($column, $key)
     * @method static mixed stripTableForPluck($column)
     * @method static mixed pluckFromObjectColumn($queryResult, $column, $key)
     * @method static mixed pluckFromArrayColumn($queryResult, $column, $key)
     * @method static mixed implode($column, $glue)
     * @method static mixed exists()
     * @method static mixed doesntExist()
     * @method static mixed existsOr(Closure $callback)
     * @method static mixed doesntExistOr(Closure $callback)
     * @method static mixed count($columns)
     * @method static mixed min($column)
     * @method static mixed max($column)
     * @method static mixed sum($column)
     * @method static mixed avg($column)
     * @method static mixed average($column)
     * @method static mixed aggregate($function, $columns)
     * @method static mixed numericAggregate($function, $columns)
     * @method static mixed setAggregate($function, $columns)
     * @method static mixed onceWithColumns($columns, $callback)
     * @method static mixed insert(array $values)
     * @method static mixed insertOrIgnore(array $values)
     * @method static mixed insertGetId(array $values, $sequence)
     * @method static mixed insertUsing(array $columns, $query)
     * @method static mixed insertOrIgnoreUsing(array $columns, $query)
     * @method static mixed update(array $values)
     * @method static mixed updateFrom(array $values)
     * @method static mixed updateOrInsert(array $attributes, callable|array $values)
     * @method static mixed upsert(array $values, $uniqueBy, $update)
     * @method static mixed increment($column, $amount, array $extra)
     * @method static mixed incrementEach(array $columns, array $extra)
     * @method static mixed decrement($column, $amount, array $extra)
     * @method static mixed decrementEach(array $columns, array $extra)
     * @method static mixed delete($id)
     * @method static mixed truncate()
     * @method static mixed newQuery()
     * @method static mixed forSubQuery()
     * @method static mixed getColumns()
     * @method static mixed raw($value)
     * @method static mixed getUnionBuilders()
     * @method static mixed getBindings()
     * @method static mixed getRawBindings()
     * @method static mixed setBindings(array $bindings, $type)
     * @method static mixed addBinding($value, $type)
     * @method static mixed castBinding($value)
     * @method static mixed mergeBindings(self $query)
     * @method static mixed cleanBindings(array $bindings)
     * @method static mixed flattenValue($value)
     * @method static mixed defaultKeyName()
     * @method static mixed getConnection()
     * @method static mixed getProcessor()
     * @method static mixed getGrammar()
     * @method static mixed useWritePdo()
     * @method static mixed isQueryable($value)
     * @method static mixed clone()
     * @method static mixed cloneWithout(array $properties)
     * @method static mixed cloneWithoutBindings(array $except)
     * @method static mixed dump($args)
     * @method static mixed dumpRawSql()
     * @method static mixed dd()
     * @method static mixed ddRawSql()
     * @method static mixed wherePast($columns)
     * @method static mixed whereNowOrPast($columns)
     * @method static mixed orWherePast($columns)
     * @method static mixed orWhereNowOrPast($columns)
     * @method static mixed whereFuture($columns)
     * @method static mixed whereNowOrFuture($columns)
     * @method static mixed orWhereFuture($columns)
     * @method static mixed orWhereNowOrFuture($columns)
     * @method static mixed wherePastOrFuture($columns, $operator, $boolean)
     * @method static mixed whereToday($columns, $boolean)
     * @method static mixed whereBeforeToday($columns)
     * @method static mixed whereTodayOrBefore($columns)
     * @method static mixed whereAfterToday($columns)
     * @method static mixed whereTodayOrAfter($columns)
     * @method static mixed orWhereToday($columns)
     * @method static mixed orWhereBeforeToday($columns)
     * @method static mixed orWhereTodayOrBefore($columns)
     * @method static mixed orWhereAfterToday($columns)
     * @method static mixed orWhereTodayOrAfter($columns)
     * @method static mixed whereTodayBeforeOrAfter($columns, $operator, $boolean)
     * @method static mixed chunk($count, callable $callback)
     * @method static mixed chunkMap(callable $callback, $count)
     * @method static mixed each(callable $callback, $count)
     * @method static mixed chunkById($count, callable $callback, $column, $alias)
     * @method static mixed chunkByIdDesc($count, callable $callback, $column, $alias)
     * @method static mixed orderedChunkById($count, callable $callback, $column, $alias, $descending)
     * @method static mixed eachById(callable $callback, $count, $column, $alias)
     * @method static mixed lazy($chunkSize)
     * @method static mixed lazyById($chunkSize, $column, $alias)
     * @method static mixed lazyByIdDesc($chunkSize, $column, $alias)
     * @method static mixed orderedLazyById($chunkSize, $column, $alias, $descending)
     * @method static mixed first($columns)
     * @method static mixed firstOrFail($columns, $message)
     * @method static mixed sole($columns)
     * @method static mixed paginateUsingCursor($perPage, $columns, $cursorName, $cursor)
     * @method static mixed getOriginalColumnNameForCursorPagination($builder, string $parameter)
     * @method static mixed paginator($items, $total, $perPage, $currentPage, $options)
     * @method static mixed simplePaginator($items, $perPage, $currentPage, $options)
     * @method static mixed cursorPaginator($items, $perPage, $cursor, $options)
     * @method static mixed tap($callback)
     * @method static mixed when($value, callable $callback, callable $default)
     * @method static mixed unless($value, callable $callback, callable $default)
     * @method static mixed explain()
     * @method static mixed forwardCallTo($object, $method, $parameters)
     * @method static mixed forwardDecoratedCallTo($object, $method, $parameters)
     * @method static mixed throwBadMethodCallException($method)
     * @method static mixed macro($name, $macro)
     * @method static mixed mixin($mixin, $replace)
     * @method static mixed hasMacro($name)
     * @method static mixed flushMacros()
     * @method static mixed macroCall($method, $parameters)
     * @mixin \Illuminate\Database\Query\Builder
     */
    class Setting extends \Illuminate\Database\Eloquent\Model
    {
        //
    }

    /**
     * App\Models\User
     *
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $last_login_at
     * @property string|null $remember_token
     * @property boolean $is_active
     * @property mixed $role
     * @property string $phone_number
     * @property string $password
     * @property \Illuminate\Support\Carbon|null $email_verified_at
     * @property string $email
     * @property string $name
     * @property int $id
     * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Booking> $bookings
     * @property-read int|null $bookings_count
     * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Review> $reviewsGiven
     * @property-read int|null $reviewsGiven_count
     * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Notification> $notifications
     * @property-read int|null $notifications_count
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereName($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereEmail($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereEmailVerifiedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User wherePassword($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User wherePhoneNumber($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereRole($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereIsActive($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereRememberToken($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereLastLoginAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User query()
     * @method static mixed select($columns)
     * @method static mixed selectSub($query, $as)
     * @method static mixed selectRaw($expression, array $bindings)
     * @method static mixed fromSub($query, $as)
     * @method static mixed fromRaw($expression, $bindings)
     * @method static mixed createSub($query)
     * @method static mixed parseSub($query)
     * @method static mixed prependDatabaseNameIfCrossDatabaseQuery($query)
     * @method static mixed addSelect($column)
     * @method static mixed distinct()
     * @method static mixed from($table, $as)
     * @method static mixed useIndex($index)
     * @method static mixed forceIndex($index)
     * @method static mixed ignoreIndex($index)
     * @method static mixed join($table, $first, $operator, $second, $type, $where)
     * @method static mixed joinWhere($table, $first, $operator, $second, $type)
     * @method static mixed joinSub($query, $as, $first, $operator, $second, $type, $where)
     * @method static mixed joinLateral($query, string $as, string $type)
     * @method static mixed leftJoinLateral($query, string $as)
     * @method static mixed leftJoin($table, $first, $operator, $second)
     * @method static mixed leftJoinWhere($table, $first, $operator, $second)
     * @method static mixed leftJoinSub($query, $as, $first, $operator, $second)
     * @method static mixed rightJoin($table, $first, $operator, $second)
     * @method static mixed rightJoinWhere($table, $first, $operator, $second)
     * @method static mixed rightJoinSub($query, $as, $first, $operator, $second)
     * @method static mixed crossJoin($table, $first, $operator, $second)
     * @method static mixed crossJoinSub($query, $as)
     * @method static mixed newJoinClause(self $parentQuery, $type, $table)
     * @method static mixed newJoinLateralClause(self $parentQuery, $type, $table)
     * @method static mixed mergeWheres($wheres, $bindings)
     * @method static mixed where($column, $operator, $value, $boolean)
     * @method static mixed addArrayOfWheres($column, $boolean, $method)
     * @method static mixed prepareValueAndOperator($value, $operator, $useDefault)
     * @method static mixed invalidOperatorAndValue($operator, $value)
     * @method static mixed invalidOperator($operator)
     * @method static mixed isBitwiseOperator($operator)
     * @method static mixed orWhere($column, $operator, $value)
     * @method static mixed whereNot($column, $operator, $value, $boolean)
     * @method static mixed orWhereNot($column, $operator, $value)
     * @method static mixed whereColumn($first, $operator, $second, $boolean)
     * @method static mixed orWhereColumn($first, $operator, $second)
     * @method static mixed whereRaw($sql, $bindings, $boolean)
     * @method static mixed orWhereRaw($sql, $bindings)
     * @method static mixed whereLike($column, $value, $caseSensitive, $boolean, $not)
     * @method static mixed orWhereLike($column, $value, $caseSensitive)
     * @method static mixed whereNotLike($column, $value, $caseSensitive, $boolean)
     * @method static mixed orWhereNotLike($column, $value, $caseSensitive)
     * @method static mixed whereIn($column, $values, $boolean, $not)
     * @method static mixed orWhereIn($column, $values)
     * @method static mixed whereNotIn($column, $values, $boolean)
     * @method static mixed orWhereNotIn($column, $values)
     * @method static mixed whereIntegerInRaw($column, $values, $boolean, $not)
     * @method static mixed orWhereIntegerInRaw($column, $values)
     * @method static mixed whereIntegerNotInRaw($column, $values, $boolean)
     * @method static mixed orWhereIntegerNotInRaw($column, $values)
     * @method static mixed whereNull($columns, $boolean, $not)
     * @method static mixed orWhereNull($column)
     * @method static mixed whereNotNull($columns, $boolean)
     * @method static mixed whereBetween($column, iterable $values, $boolean, $not)
     * @method static mixed whereBetweenColumns($column, array $values, $boolean, $not)
     * @method static mixed orWhereBetween($column, iterable $values)
     * @method static mixed orWhereBetweenColumns($column, array $values)
     * @method static mixed whereNotBetween($column, iterable $values, $boolean)
     * @method static mixed whereNotBetweenColumns($column, array $values, $boolean)
     * @method static mixed orWhereNotBetween($column, iterable $values)
     * @method static mixed orWhereNotBetweenColumns($column, array $values)
     * @method static mixed orWhereNotNull($column)
     * @method static mixed whereDate($column, $operator, $value, $boolean)
     * @method static mixed orWhereDate($column, $operator, $value)
     * @method static mixed whereTime($column, $operator, $value, $boolean)
     * @method static mixed orWhereTime($column, $operator, $value)
     * @method static mixed whereDay($column, $operator, $value, $boolean)
     * @method static mixed orWhereDay($column, $operator, $value)
     * @method static mixed whereMonth($column, $operator, $value, $boolean)
     * @method static mixed orWhereMonth($column, $operator, $value)
     * @method static mixed whereYear($column, $operator, $value, $boolean)
     * @method static mixed orWhereYear($column, $operator, $value)
     * @method static mixed addDateBasedWhere($type, $column, $operator, $value, $boolean)
     * @method static mixed whereNested(Closure $callback, $boolean)
     * @method static mixed forNestedWhere()
     * @method static mixed addNestedWhereQuery($query, $boolean)
     * @method static mixed whereSub($column, $operator, $callback, $boolean)
     * @method static mixed whereExists($callback, $boolean, $not)
     * @method static mixed orWhereExists($callback, $not)
     * @method static mixed whereNotExists($callback, $boolean)
     * @method static mixed orWhereNotExists($callback)
     * @method static mixed addWhereExistsQuery(self $query, $boolean, $not)
     * @method static mixed whereRowValues($columns, $operator, $values, $boolean)
     * @method static mixed orWhereRowValues($columns, $operator, $values)
     * @method static mixed whereJsonContains($column, $value, $boolean, $not)
     * @method static mixed orWhereJsonContains($column, $value)
     * @method static mixed whereJsonDoesntContain($column, $value, $boolean)
     * @method static mixed orWhereJsonDoesntContain($column, $value)
     * @method static mixed whereJsonOverlaps($column, $value, $boolean, $not)
     * @method static mixed orWhereJsonOverlaps($column, $value)
     * @method static mixed whereJsonDoesntOverlap($column, $value, $boolean)
     * @method static mixed orWhereJsonDoesntOverlap($column, $value)
     * @method static mixed whereJsonContainsKey($column, $boolean, $not)
     * @method static mixed orWhereJsonContainsKey($column)
     * @method static mixed whereJsonDoesntContainKey($column, $boolean)
     * @method static mixed orWhereJsonDoesntContainKey($column)
     * @method static mixed whereJsonLength($column, $operator, $value, $boolean)
     * @method static mixed orWhereJsonLength($column, $operator, $value)
     * @method static mixed dynamicWhere($method, $parameters)
     * @method static mixed addDynamic($segment, $connector, $parameters, $index)
     * @method static mixed whereFullText($columns, $value, array $options, $boolean)
     * @method static mixed orWhereFullText($columns, $value, array $options)
     * @method static mixed whereAll($columns, $operator, $value, $boolean)
     * @method static mixed orWhereAll($columns, $operator, $value)
     * @method static mixed whereAny($columns, $operator, $value, $boolean)
     * @method static mixed orWhereAny($columns, $operator, $value)
     * @method static mixed whereNone($columns, $operator, $value, $boolean)
     * @method static mixed orWhereNone($columns, $operator, $value)
     * @method static mixed groupBy($groups)
     * @method static mixed groupByRaw($sql, array $bindings)
     * @method static mixed having($column, $operator, $value, $boolean)
     * @method static mixed orHaving($column, $operator, $value)
     * @method static mixed havingNested(Closure $callback, $boolean)
     * @method static mixed addNestedHavingQuery($query, $boolean)
     * @method static mixed havingNull($columns, $boolean, $not)
     * @method static mixed orHavingNull($column)
     * @method static mixed havingNotNull($columns, $boolean)
     * @method static mixed orHavingNotNull($column)
     * @method static mixed havingBetween($column, iterable $values, $boolean, $not)
     * @method static mixed havingRaw($sql, array $bindings, $boolean)
     * @method static mixed orHavingRaw($sql, array $bindings)
     * @method static mixed orderBy($column, $direction)
     * @method static mixed orderByDesc($column)
     * @method static mixed latest($column)
     * @method static mixed oldest($column)
     * @method static mixed inRandomOrder($seed)
     * @method static mixed orderByRaw($sql, $bindings)
     * @method static mixed skip($value)
     * @method static mixed offset($value)
     * @method static mixed take($value)
     * @method static mixed limit($value)
     * @method static mixed groupLimit($value, $column)
     * @method static mixed forPage($page, $perPage)
     * @method static mixed forPageBeforeId($perPage, $lastId, $column)
     * @method static mixed forPageAfterId($perPage, $lastId, $column)
     * @method static mixed reorder($column, $direction)
     * @method static mixed removeExistingOrdersFor($column)
     * @method static mixed union($query, $all)
     * @method static mixed unionAll($query)
     * @method static mixed lock($value)
     * @method static mixed lockForUpdate()
     * @method static mixed sharedLock()
     * @method static mixed beforeQuery(callable $callback)
     * @method static mixed applyBeforeQueryCallbacks()
     * @method static mixed afterQuery(Closure $callback)
     * @method static mixed applyAfterQueryCallbacks($result)
     * @method static mixed toSql()
     * @method static mixed toRawSql()
     * @method static mixed find($id, $columns)
     * @method static mixed findOr($id, $columns, Closure $callback)
     * @method static mixed value($column)
     * @method static mixed rawValue(string $expression, array $bindings)
     * @method static mixed soleValue($column)
     * @method static mixed get($columns)
     * @method static mixed runSelect()
     * @method static mixed withoutGroupLimitKeys($items)
     * @method static mixed paginate($perPage, $columns, $pageName, $page, $total)
     * @method static mixed simplePaginate($perPage, $columns, $pageName, $page)
     * @method static mixed cursorPaginate($perPage, $columns, $cursorName, $cursor)
     * @method static mixed ensureOrderForCursorPagination($shouldReverse)
     * @method static mixed getCountForPagination($columns)
     * @method static mixed runPaginationCountQuery($columns)
     * @method static mixed cloneForPaginationCount()
     * @method static mixed withoutSelectAliases(array $columns)
     * @method static mixed cursor()
     * @method static mixed enforceOrderBy()
     * @method static mixed pluck($column, $key)
     * @method static mixed stripTableForPluck($column)
     * @method static mixed pluckFromObjectColumn($queryResult, $column, $key)
     * @method static mixed pluckFromArrayColumn($queryResult, $column, $key)
     * @method static mixed implode($column, $glue)
     * @method static mixed exists()
     * @method static mixed doesntExist()
     * @method static mixed existsOr(Closure $callback)
     * @method static mixed doesntExistOr(Closure $callback)
     * @method static mixed count($columns)
     * @method static mixed min($column)
     * @method static mixed max($column)
     * @method static mixed sum($column)
     * @method static mixed avg($column)
     * @method static mixed average($column)
     * @method static mixed aggregate($function, $columns)
     * @method static mixed numericAggregate($function, $columns)
     * @method static mixed setAggregate($function, $columns)
     * @method static mixed onceWithColumns($columns, $callback)
     * @method static mixed insert(array $values)
     * @method static mixed insertOrIgnore(array $values)
     * @method static mixed insertGetId(array $values, $sequence)
     * @method static mixed insertUsing(array $columns, $query)
     * @method static mixed insertOrIgnoreUsing(array $columns, $query)
     * @method static mixed update(array $values)
     * @method static mixed updateFrom(array $values)
     * @method static mixed updateOrInsert(array $attributes, callable|array $values)
     * @method static mixed upsert(array $values, $uniqueBy, $update)
     * @method static mixed increment($column, $amount, array $extra)
     * @method static mixed incrementEach(array $columns, array $extra)
     * @method static mixed decrement($column, $amount, array $extra)
     * @method static mixed decrementEach(array $columns, array $extra)
     * @method static mixed delete($id)
     * @method static mixed truncate()
     * @method static mixed newQuery()
     * @method static mixed forSubQuery()
     * @method static mixed getColumns()
     * @method static mixed raw($value)
     * @method static mixed getUnionBuilders()
     * @method static mixed getBindings()
     * @method static mixed getRawBindings()
     * @method static mixed setBindings(array $bindings, $type)
     * @method static mixed addBinding($value, $type)
     * @method static mixed castBinding($value)
     * @method static mixed mergeBindings(self $query)
     * @method static mixed cleanBindings(array $bindings)
     * @method static mixed flattenValue($value)
     * @method static mixed defaultKeyName()
     * @method static mixed getConnection()
     * @method static mixed getProcessor()
     * @method static mixed getGrammar()
     * @method static mixed useWritePdo()
     * @method static mixed isQueryable($value)
     * @method static mixed clone()
     * @method static mixed cloneWithout(array $properties)
     * @method static mixed cloneWithoutBindings(array $except)
     * @method static mixed dump($args)
     * @method static mixed dumpRawSql()
     * @method static mixed dd()
     * @method static mixed ddRawSql()
     * @method static mixed wherePast($columns)
     * @method static mixed whereNowOrPast($columns)
     * @method static mixed orWherePast($columns)
     * @method static mixed orWhereNowOrPast($columns)
     * @method static mixed whereFuture($columns)
     * @method static mixed whereNowOrFuture($columns)
     * @method static mixed orWhereFuture($columns)
     * @method static mixed orWhereNowOrFuture($columns)
     * @method static mixed wherePastOrFuture($columns, $operator, $boolean)
     * @method static mixed whereToday($columns, $boolean)
     * @method static mixed whereBeforeToday($columns)
     * @method static mixed whereTodayOrBefore($columns)
     * @method static mixed whereAfterToday($columns)
     * @method static mixed whereTodayOrAfter($columns)
     * @method static mixed orWhereToday($columns)
     * @method static mixed orWhereBeforeToday($columns)
     * @method static mixed orWhereTodayOrBefore($columns)
     * @method static mixed orWhereAfterToday($columns)
     * @method static mixed orWhereTodayOrAfter($columns)
     * @method static mixed whereTodayBeforeOrAfter($columns, $operator, $boolean)
     * @method static mixed chunk($count, callable $callback)
     * @method static mixed chunkMap(callable $callback, $count)
     * @method static mixed each(callable $callback, $count)
     * @method static mixed chunkById($count, callable $callback, $column, $alias)
     * @method static mixed chunkByIdDesc($count, callable $callback, $column, $alias)
     * @method static mixed orderedChunkById($count, callable $callback, $column, $alias, $descending)
     * @method static mixed eachById(callable $callback, $count, $column, $alias)
     * @method static mixed lazy($chunkSize)
     * @method static mixed lazyById($chunkSize, $column, $alias)
     * @method static mixed lazyByIdDesc($chunkSize, $column, $alias)
     * @method static mixed orderedLazyById($chunkSize, $column, $alias, $descending)
     * @method static mixed first($columns)
     * @method static mixed firstOrFail($columns, $message)
     * @method static mixed sole($columns)
     * @method static mixed paginateUsingCursor($perPage, $columns, $cursorName, $cursor)
     * @method static mixed getOriginalColumnNameForCursorPagination($builder, string $parameter)
     * @method static mixed paginator($items, $total, $perPage, $currentPage, $options)
     * @method static mixed simplePaginator($items, $perPage, $currentPage, $options)
     * @method static mixed cursorPaginator($items, $perPage, $cursor, $options)
     * @method static mixed tap($callback)
     * @method static mixed when($value, callable $callback, callable $default)
     * @method static mixed unless($value, callable $callback, callable $default)
     * @method static mixed explain()
     * @method static mixed forwardCallTo($object, $method, $parameters)
     * @method static mixed forwardDecoratedCallTo($object, $method, $parameters)
     * @method static mixed throwBadMethodCallException($method)
     * @method static mixed macro($name, $macro)
     * @method static mixed mixin($mixin, $replace)
     * @method static mixed hasMacro($name)
     * @method static mixed flushMacros()
     * @method static mixed macroCall($method, $parameters)
     * @mixin \Illuminate\Database\Query\Builder
     */
    class User extends \Illuminate\Foundation\Auth\User
    {
        //
    }

}