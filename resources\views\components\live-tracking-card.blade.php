@props(['booking', 'height' => '400px', 'showDetails' => true])


<div class="bg-white rounded-xl shadow-sm overflow-hidden">
    @if($showDetails)
        <div class="p-6 border-b border-gray-200">
            <div class="flex justify-between items-start">
                <div>
                    <h3 class="text-lg font-bold text-gray-900">Live Tracking</h3>
                    <p class="text-sm text-gray-600">{{ $booking->booking_id }}</p>
                </div>
                <div class="text-right">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        @switch($booking->status)
                            @case('confirmed')
                                bg-blue-100 text-blue-800
                                @break

                            @case('in_progress')
                                bg-purple-100 text-purple-800
                                @break
                            @case('completed')
                                bg-green-100 text-green-800
                                @break
                            @default
                                bg-gray-100 text-gray-800
                        @endswitch
                    ">
                        <span class="booking-status-text">{{ ucfirst(str_replace('_', ' ', $booking->status)) }}</span>
                    </span>
                </div>
            </div>
        </div>
    @endif
    
    <!-- Map Container -->
    <div class="relative">
        <div id="trackingMap{{ $booking->id }}" style="height: {{ $height }}; width: 100%;"></div>
        
        <!-- Loading Overlay -->
        <div id="mapLoading{{ $booking->id }}" class="absolute inset-0 bg-gray-100 flex items-center justify-center">
            <div class="text-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-2"></div>
                <p class="text-sm text-gray-600">Loading map...</p>
            </div>
        </div>
        
        <!-- Map Controls -->
        <div class="absolute top-4 right-4 space-y-2">
            <button onclick="refreshTrackingMap{{ $booking->id }}()" 
                    class="bg-white shadow-lg rounded-lg p-2 hover:bg-gray-50 transition-colors">
                <i class="fas fa-sync-alt text-gray-600"></i>
            </button>

            <button onclick="showFullRoute{{ $booking->id }}()" 
                    class="bg-white shadow-lg rounded-lg p-2 hover:bg-gray-50 transition-colors">
                <i class="fas fa-route text-gray-600"></i>
            </button>
        </div>
    </div>
    
    @if($showDetails)
        <!-- Tracking Details -->
        <div class="p-6 bg-gray-50">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Route Information -->
                <div>
                    <h4 class="font-medium text-gray-900 mb-3">Route Information</h4>
                    <div class="space-y-2">
                        <div class="flex items-start">
                            <div class="w-3 h-3 bg-green-500 rounded-full mt-1 mr-3"></div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-900">Pickup</p>
                                <p class="text-xs text-gray-600">{{ $booking->pickup_address }}</p>
                                <p class="text-xs text-gray-500">{{ $booking->pickup_person_name }} - {{ $booking->pickup_person_phone }}</p>
                            </div>
                        </div>
                        <div class="flex items-center ml-1.5">
                            <div class="w-px h-6 bg-gray-300"></div>
                        </div>
                        <div class="flex items-start">
                            <div class="w-3 h-3 bg-red-500 rounded-full mt-1 mr-3"></div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-900">Delivery</p>
                                <p class="text-xs text-gray-600">{{ $booking->delivery_address }}</p>
                                <p class="text-xs text-gray-500">{{ $booking->receiver_name }} - {{ $booking->receiver_phone }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Delivery Information -->
                <div>
                    <h4 class="font-medium text-gray-900 mb-3">Delivery Status</h4>
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-truck text-blue-600"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">{{ ucfirst(str_replace('_', ' ', $booking->status)) }}</p>
                            <p class="text-xs text-gray-600">Booking #{{ $booking->booking_id }}</p>
                        </div>
                    </div>

                        <div class="space-y-2">
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-gray-500">Status</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <span class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-1"></span>
                                    {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                </span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-gray-500">Created</span>
                                <span class="text-xs text-gray-900">{{ $booking->created_at->diffForHumans() }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-gray-500">ETA</span>
                                <span class="text-xs text-gray-900" id="eta{{ $booking->id }}">Calculating...</span>
                            </div>
                        </div>
                </div>
            </div>
            
            <!-- Progress Timeline -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <h4 class="font-medium text-gray-900 mb-3">Delivery Progress</h4>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <div class="w-3 h-3 {{ in_array($booking->status, ['confirmed', 'delivery_enroute', 'in_progress', 'completed']) ? 'bg-green-500' : 'bg-gray-300' }} rounded-full"></div>
                        <span class="ml-2 text-xs text-gray-600">Confirmed</span>
                    </div>
                    <div class="flex-1 h-px {{ in_array($booking->status, ['delivery_enroute', 'in_progress', 'completed']) ? 'bg-green-500' : 'bg-gray-300' }}"></div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 {{ in_array($booking->status, ['in_progress', 'completed']) ? 'bg-green-500' : 'bg-gray-300' }} rounded-full"></div>
                        <span class="ml-2 text-xs text-gray-600">Delivery Enroute</span>
                    </div>
                    <div class="flex-1 h-px {{ in_array($booking->status, ['in_progress', 'completed']) ? 'bg-green-500' : 'bg-gray-300' }}"></div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 {{ in_array($booking->status, ['in_progress', 'completed']) ? 'bg-green-500' : 'bg-gray-300' }} rounded-full"></div>
                        <span class="ml-2 text-xs text-gray-600">In Transit</span>
                    </div>
                    <div class="flex-1 h-px {{ $booking->status === 'completed' ? 'bg-green-500' : 'bg-gray-300' }}"></div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 {{ $booking->status === 'completed' ? 'bg-green-500' : 'bg-gray-300' }} rounded-full"></div>
                        <span class="ml-2 text-xs text-gray-600">Delivered</span>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

@push('scripts')
<script>
// Map variables for booking {{ $booking->id }}
let map{{ $booking->id }};
let directionsService{{ $booking->id }};
let directionsRenderer{{ $booking->id }};
let pickupMarker{{ $booking->id }};
let deliveryMarker{{ $booking->id }};

// Initialize tracking map for this booking
function initTrackingMap{{ $booking->id }}() {
    if (typeof google === 'undefined' || !google.maps) {
        console.warn('Google Maps API not loaded yet for booking {{ $booking->id }}');
        return;
    }

    // Initialize the route preview map
    const routeMapElement = document.getElementById('booking-map-{{ $booking->id }}');
    if (routeMapElement) {
        map{{ $booking->id }} = new google.maps.Map(routeMapElement, {
            zoom: 12,
            center: { lat: 5.6037, lng: -0.1870 }, // Accra, Ghana
            mapTypeId: google.maps.MapTypeId.ROADMAP,
            styles: [
                {
                    featureType: 'poi',
                    elementType: 'labels',
                    stylers: [{ visibility: 'off' }]
                }
            ]
        });

        directionsService{{ $booking->id }} = new google.maps.DirectionsService();
        directionsRenderer{{ $booking->id }} = new google.maps.DirectionsRenderer({
            suppressMarkers: true,
            polylineOptions: {
                strokeColor: '#F59E0B',
                strokeWeight: 4
            }
        });
        directionsRenderer{{ $booking->id }}.setMap(map{{ $booking->id }});

        // Load booking route
        updateMapAndRoute{{ $booking->id }}();
    }

    // Map toggle functionality
    const toggleButton = document.getElementById('toggle-map-{{ $booking->id }}');
    if (toggleButton) {
        toggleButton.addEventListener('click', function() {
            const mapContainer = document.getElementById('booking-map-{{ $booking->id }}');
            const button = this;

            if (mapContainer.style.height === '200px') {
                mapContainer.style.height = '400px';
                button.innerHTML = '<i class="fas fa-compress-arrows-alt mr-1"></i>Collapse';
            } else {
                mapContainer.style.height = '200px';
                button.innerHTML = '<i class="fas fa-expand-arrows-alt mr-1"></i>Expand';
            }

            // Trigger map resize
            setTimeout(() => {
                if (map{{ $booking->id }}) {
                    google.maps.event.trigger(map{{ $booking->id }}, 'resize');
                    if (pickupMarker{{ $booking->id }} && deliveryMarker{{ $booking->id }}) {
                        const bounds = new google.maps.LatLngBounds();
                        bounds.extend(pickupMarker{{ $booking->id }}.getPosition());
                        bounds.extend(deliveryMarker{{ $booking->id }}.getPosition());
                        map{{ $booking->id }}.fitBounds(bounds);
                    }
                }
            }, 300);
        });
    }

    // Initialize the live tracking map
    const mapElement = document.getElementById('trackingMap{{ $booking->id }}');
    const loadingElement = document.getElementById('mapLoading{{ $booking->id }}');

    if (mapElement) {
        // Default center between pickup and delivery
        const pickupLat = {{ $booking->pickup_latitude ?? 5.6037 }};
        const pickupLng = {{ $booking->pickup_longitude ?? -0.1870 }};
        const deliveryLat = {{ $booking->delivery_latitude ?? 5.6037 }};
        const deliveryLng = {{ $booking->delivery_longitude ?? -0.1870 }};

        const centerLat = (pickupLat + deliveryLat) / 2;
        const centerLng = (pickupLng + deliveryLng) / 2;

        const liveTrackingMap = new google.maps.Map(mapElement, {
            zoom: 13,
            center: { lat: centerLat, lng: centerLng },
            mapTypeId: google.maps.MapTypeId.ROADMAP
        });

        // Store map reference globally
        window.trackingMap{{ $booking->id }} = liveTrackingMap;
        let vehicleMarker{{ $booking->id }} = null;

        // Add pickup marker for live tracking
        const livePickupMarker = new google.maps.Marker({
            position: { lat: pickupLat, lng: pickupLng },
            map: liveTrackingMap,
            title: 'Pickup Location',
            icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" fill="#10B981" stroke="white" stroke-width="2"/>
                        <path d="M8 12l2 2 4-4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                `),
                scaledSize: new google.maps.Size(32, 32)
            }
        });

        // Add delivery marker for live tracking
        const liveDeliveryMarker = new google.maps.Marker({
            position: { lat: deliveryLat, lng: deliveryLng },
            map: liveTrackingMap,
            title: 'Delivery Location',
            icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" fill="#EF4444" stroke="white" stroke-width="2"/>
                        <path d="M9 12l2 2 4-4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                `),
                scaledSize: new google.maps.Size(32, 32)
            }
        });

        // Initial route calculation for live tracking
        calculateAndDisplayRoute{{ $booking->id }}(pickupLat, pickupLng, deliveryLat, deliveryLng, liveTrackingMap);

        // Start live tracking
        setInterval(() => refreshTrackingMap{{ $booking->id }}(), 15000); // Refresh every 15 seconds

        // Info windows for live tracking
        const livePickupInfoWindow = new google.maps.InfoWindow({
            content: `
                <div class="p-2">
                    <h4 class="font-bold">Pickup Location</h4>
                    <p class="text-sm">{{ $booking->pickup_address }}</p>
                    <p class="text-xs text-gray-600">{{ $booking->pickup_person_name }}</p>
                </div>
            `
        });

        const liveDeliveryInfoWindow = new google.maps.InfoWindow({
            content: `
                <div class="p-2">
                    <h4 class="font-bold">Delivery Location</h4>
                    <p class="text-sm">{{ $booking->delivery_address }}</p>
                    <p class="text-xs text-gray-600">{{ $booking->receiver_name }}</p>
                </div>
            `
        });

        livePickupMarker.addListener('click', () => {
            livePickupInfoWindow.open(liveTrackingMap, livePickupMarker);
        });

        liveDeliveryMarker.addListener('click', () => {
            liveDeliveryInfoWindow.open(liveTrackingMap, liveDeliveryMarker);
        });

        // Fit map to show all markers
        const bounds = new google.maps.LatLngBounds();
        bounds.extend({ lat: pickupLat, lng: pickupLng });
        bounds.extend({ lat: deliveryLat, lng: deliveryLng });

        liveTrackingMap.fitBounds(bounds);

        // Hide loading overlay
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
    }
}

function updateMapAndRoute{{ $booking->id }}() {
    const pickupLat = {{ $booking->pickup_latitude ?? 5.6037 }};
    const pickupLng = {{ $booking->pickup_longitude ?? -0.1870 }};
    const deliveryLat = {{ $booking->delivery_latitude ?? 5.6037 }};
    const deliveryLng = {{ $booking->delivery_longitude ?? -0.1870 }};

    if (!map{{ $booking->id }} || isNaN(pickupLat) || isNaN(pickupLng) || isNaN(deliveryLat) || isNaN(deliveryLng)) {
        return;
    }

    // Clear existing markers
    if (pickupMarker{{ $booking->id }}) pickupMarker{{ $booking->id }}.setMap(null);
    if (deliveryMarker{{ $booking->id }}) deliveryMarker{{ $booking->id }}.setMap(null);

    // Add pickup marker
    pickupMarker{{ $booking->id }} = new google.maps.Marker({
        position: { lat: pickupLat, lng: pickupLng },
        map: map{{ $booking->id }},
        title: 'Pickup Location',
        icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="10" fill="#10B981" stroke="white" stroke-width="2"/>
                    <path d="M8 12l2 2 4-4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            `),
            scaledSize: new google.maps.Size(32, 32)
        }
    });

    // Add delivery marker
    deliveryMarker{{ $booking->id }} = new google.maps.Marker({
        position: { lat: deliveryLat, lng: deliveryLng },
        map: map{{ $booking->id }},
        title: 'Delivery Location',
        icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="10" fill="#EF4444" stroke="white" stroke-width="2"/>
                    <path d="M12 8v4l3 3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            `),
            scaledSize: new google.maps.Size(32, 32)
        }
    });

    // Add info windows
    const pickupInfoWindow = new google.maps.InfoWindow({
        content: `
            <div class="p-2">
                <h4 class="font-bold">Pickup Location</h4>
                <p class="text-sm">{{ $booking->pickup_address }}</p>
                <p class="text-xs text-gray-600">{{ $booking->pickup_person_name }}</p>
            </div>
        `
    });

    const deliveryInfoWindow = new google.maps.InfoWindow({
        content: `
            <div class="p-2">
                <h4 class="font-bold">Delivery Location</h4>
                <p class="text-sm">{{ $booking->delivery_address }}</p>
                <p class="text-xs text-gray-600">{{ $booking->receiver_name }}</p>
            </div>
        `
    });

    pickupMarker{{ $booking->id }}.addListener('click', () => {
        pickupInfoWindow.open(map{{ $booking->id }}, pickupMarker{{ $booking->id }});
    });

    deliveryMarker{{ $booking->id }}.addListener('click', () => {
        deliveryInfoWindow.open(map{{ $booking->id }}, deliveryMarker{{ $booking->id }});
    });

    // Calculate and display route
    directionsService{{ $booking->id }}.route({
        origin: { lat: pickupLat, lng: pickupLng },
        destination: { lat: deliveryLat, lng: deliveryLng },
        travelMode: google.maps.TravelMode.DRIVING
    }, function(result, status) {
        if (status === 'OK') {
            directionsRenderer{{ $booking->id }}.setDirections(result);

            // Update distance and duration if not already set
            const route = result.routes[0];
            if (route && route.legs && route.legs.length > 0) {
                const leg = route.legs[0];
                const distanceElement = document.getElementById('estimated-distance-{{ $booking->id }}');
                const durationElement = document.getElementById('estimated-duration-{{ $booking->id }}');

                if (distanceElement && distanceElement.textContent === '-- km') {
                    distanceElement.textContent = leg.distance.text;
                }
                if (durationElement && durationElement.textContent === '-- mins') {
                    durationElement.textContent = leg.duration.text;
                }
            }

            // Fit map to show route
            const bounds = new google.maps.LatLngBounds();
            bounds.extend({ lat: pickupLat, lng: pickupLng });
            bounds.extend({ lat: deliveryLat, lng: deliveryLng });
            map{{ $booking->id }}.fitBounds(bounds);
        }
    });
}

// Refresh tracking map
function refreshTrackingMap{{ $booking->id }}() {
    fetch(`/api/live-map/booking/{{ $booking->id }}`)
        .then(response => response.json())
        .then(data => {
            // Update booking status if changed
            const statusElement = document.querySelector('.booking-status-text');
            if (statusElement && data.status) {
                statusElement.textContent = data.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
            }

            // Update any other real-time data as needed
            console.log('📍 Tracking data updated for booking:', data.booking_id);
        })
        .catch(error => console.error('Error refreshing tracking data:', error));
}

function calculateAndDisplayRoute{{ $booking->id }}(originLat, originLng, destLat, destLng, targetMap = null) {
    const directionsService = new google.maps.DirectionsService();
    const directionsRenderer = new google.maps.DirectionsRenderer({
        suppressMarkers: true,
        polylineOptions: {
            strokeColor: '#F59E0B',
            strokeWeight: 4
        }
    });

    // Use the provided map or the live tracking map
    const mapToUse = targetMap || window.trackingMap{{ $booking->id }};
    if (mapToUse) {
        directionsRenderer.setMap(mapToUse);

        directionsService.route({
            origin: { lat: originLat, lng: originLng },
            destination: { lat: destLat, lng: destLng },
            travelMode: google.maps.TravelMode.DRIVING
        }, (result, status) => {
            if (status === 'OK') {
                directionsRenderer.setDirections(result);
                const route = result.routes[0];
                if (route && route.legs && route.legs.length > 0) {
                    const leg = route.legs[0];
                    const etaElement = document.getElementById('eta{{ $booking->id }}');
                    if (etaElement) {
                        etaElement.textContent = leg.duration.text;
                    }
                }
            }
        });
    }
}

// Show full route
function showFullRoute{{ $booking->id }}() {
    if (window.trackingMap{{ $booking->id }}) {
        const bounds = new google.maps.LatLngBounds();
        bounds.extend({ lat: {{ $booking->pickup_latitude ?? 5.6037 }}, lng: {{ $booking->pickup_longitude ?? -0.1870 }} });
        bounds.extend({ lat: {{ $booking->delivery_latitude ?? 5.6037 }}, lng: {{ $booking->delivery_longitude ?? -0.1870 }} });

        window.trackingMap{{ $booking->id }}.fitBounds(bounds);
    }
}

// Initialize when Google Maps is loaded
if (typeof google !== 'undefined' && google.maps) {
    initTrackingMap{{ $booking->id }}();
} else {
    // Wait for Google Maps to load
    window.addEventListener('load', () => {
        if (typeof google !== 'undefined' && google.maps) {
            initTrackingMap{{ $booking->id }}();
        }
    });
}
</script>
@endpush
