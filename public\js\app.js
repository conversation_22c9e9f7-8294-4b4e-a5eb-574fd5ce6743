/* TTAJet Courier Service - Unified JavaScript */

document.addEventListener('DOMContentLoaded', () => {
    // Ensure all elements are visible by default (fallback)
    ensureElementsVisible();

    // Initialize GSAP and ScrollTrigger
    if (typeof gsap !== 'undefined') {
        try {
            gsap.registerPlugin(ScrollTrigger);
            initializeAnimations();
        } catch (error) {
            console.warn('GSAP animation failed, elements will remain visible:', error);
            ensureElementsVisible();
        }
    } else {
        console.warn('GSAP not loaded, elements will remain visible without animations');
    }

    // Initialize components
    initializeScrollToTop();
    initializeReviewCarousel();
    initializeFormEnhancements();
    initializeToastNotifications();
});

// Fallback function to ensure elements are visible
function ensureElementsVisible() {
    // Only target elements that actually exist on the page
    const selectors = ['.text-content > *', '.booking-form', '.service-card', '.price-card', '#about-preview *', '#reviews *'];

    selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
            elements.forEach(el => {
                el.style.opacity = '1';
                el.style.transform = 'translateY(0) scale(1)';
                el.style.visibility = 'visible';
            });
        }
    });
}

// GSAP Animations
function initializeAnimations() {
    // Only run homepage animations if we're not on a booking page
    if (document.body.classList.contains('booking-page')) {
        console.log('Booking page detected, skipping homepage GSAP animations');
        return;
    }

    // Add class to enable animations only when GSAP is working
    document.body.classList.add('js-animations-enabled');

    // Set initial states to ensure elements are visible by default - only for elements that exist
    const homePageSelectors = ['.text-content > *', '.booking-form', '.service-card', '.price-card'];
    homePageSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
            gsap.set(elements, {
                opacity: 1,
                y: 0,
                scale: 1
            });
        }
    });

    // Hero section animations - only run on homepage
    const textContent = document.querySelector('.text-content');
    const bookingForm = document.querySelector('.booking-form');

    if (textContent || bookingForm) {
        const tl = gsap.timeline({defaults: {ease: 'power3.out'}});

        if (textContent) {
            const textElements = document.querySelectorAll('.text-content > *');
            if (textElements.length > 0) {
                gsap.set(textElements, {opacity: 0, y: 30});
                tl.to(textElements, {
                    opacity: 1,
                    y: 0,
                    stagger: 0.2,
                    duration: 1
                });
            }
        }

        if (bookingForm) {
            gsap.set(bookingForm, {opacity: 0, scale: 0.95});
            tl.to(bookingForm, {
                opacity: 1,
                scale: 1,
                duration: 0.8
            }, "-=0.6");
        }
    }

    // About section animation with better error handling
    const aboutSection = document.querySelector("#about-preview");
    if (aboutSection) {
        const aboutElements = aboutSection.querySelectorAll('*');
        if (aboutElements.length > 0) {
            gsap.set(aboutElements, {opacity: 1}); // Ensure visible by default
            gsap.fromTo(aboutElements,
                { opacity: 0, y: 50 },
                {
                    scrollTrigger: {
                        trigger: "#about-preview",
                        start: "top 80%",
                        toggleActions: "play none none reverse",
                        onRefresh: () => gsap.set(aboutElements, {opacity: 1}) // Fallback
                    },
                    opacity: 1,
                    y: 0,
                    stagger: 0.2,
                    duration: 1
                }
            );
        }
    }
    
    // Service cards animation - FIXED opacity bug
    const serviceCards = document.querySelectorAll(".service-card");
    if (serviceCards.length > 0) {
        gsap.set(serviceCards, {opacity: 1}); // Ensure visible by default
        gsap.fromTo(serviceCards,
            { opacity: 0, y: 50 },
            {
                scrollTrigger: {
                    trigger: "#services",
                    start: "top 80%",
                    toggleActions: "play none none reverse",
                    onRefresh: () => gsap.set(serviceCards, {opacity: 1}) // Fallback
                },
                opacity: 1,
                y: 0,
                stagger: 0.2,
                duration: 0.8
            }
        );
    }

    // Price cards animation
    const priceCards = document.querySelectorAll(".price-card");
    if (priceCards.length > 0) {
        gsap.set(priceCards, {opacity: 1}); // Ensure visible by default
        gsap.fromTo(priceCards,
            { opacity: 0, y: 50 },
            {
                scrollTrigger: {
                    trigger: "#pricing",
                    start: "top 80%",
                    toggleActions: "play none none reverse",
                    onRefresh: () => gsap.set(priceCards, {opacity: 1}) // Fallback
                },
                opacity: 1,
                y: 0,
                stagger: 0.2,
                duration: 0.8
            }
        );
    }

    // Reviews section animation
    const reviewsSection = document.querySelector("#reviews");
    if (reviewsSection) {
        const reviewElements = reviewsSection.querySelectorAll('.review-text-content > *, .relative');
        if (reviewElements.length > 0) {
            gsap.set(reviewElements, {opacity: 1}); // Ensure visible by default
            gsap.fromTo(reviewElements,
                { opacity: 0, y: 50 },
                {
                    scrollTrigger: {
                        trigger: "#reviews",
                        start: "top 80%",
                        toggleActions: "play none none reverse",
                        onRefresh: () => gsap.set(reviewElements, {opacity: 1}) // Fallback
                    },
                    opacity: 1,
                    y: 0,
                    stagger: 0.3,
                    duration: 1
                }
            );
        }
    }
}

// Scroll to Top functionality
function initializeScrollToTop() {
    const scrollToTopBtn = document.getElementById('scroll-to-top');

    if (scrollToTopBtn) {
        // Ensure button starts hidden
        scrollToTopBtn.style.opacity = '0';
        scrollToTopBtn.style.visibility = 'hidden';
        scrollToTopBtn.style.transform = 'scale(0.9)';

        // Show/hide button based on scroll position
        if (typeof gsap !== 'undefined' && typeof ScrollTrigger !== 'undefined') {
            try {
                gsap.to(scrollToTopBtn, {
                    scrollTrigger: {
                        trigger: "body",
                        start: "500px top",
                        end: "bottom bottom",
                        toggleActions: "play reverse play reverse",
                        onUpdate: (self) => {
                            // Additional check to ensure button visibility
                            if (self.progress > 0) {
                                scrollToTopBtn.style.opacity = '1';
                                scrollToTopBtn.style.visibility = 'visible';
                                scrollToTopBtn.style.transform = 'scale(1)';
                            }
                        }
                    },
                    opacity: 1,
                    visibility: "visible",
                    scale: 1,
                    duration: 0.3
                });
            } catch (error) {
                console.warn('ScrollTrigger failed, using fallback scroll handler:', error);
                initScrollFallback();
            }
        } else {
            initScrollFallback();
        }

        // Scroll to top on click
        scrollToTopBtn.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        function initScrollFallback() {
            // Fallback without GSAP
            const handleScroll = () => {
                if (window.pageYOffset > 500) {
                    scrollToTopBtn.style.opacity = '1';
                    scrollToTopBtn.style.visibility = 'visible';
                    scrollToTopBtn.style.transform = 'scale(1)';
                } else {
                    scrollToTopBtn.style.opacity = '0';
                    scrollToTopBtn.style.visibility = 'hidden';
                    scrollToTopBtn.style.transform = 'scale(0.9)';
                }
            };

            window.addEventListener('scroll', handleScroll);
            // Initial check
            handleScroll();
        }
    }
}

// Review Carousel functionality
function initializeReviewCarousel() {
    const reviewsData = [
        {
            quote: '"As a busy professional, managing shipments can be challenging, but TTAJet simplifies this task by providing me with the tools to stay on top of my logistics."',
            name: 'Ama Serwaa',
            role: 'CEO, Serwaa Enterprises'
        },
        {
            quote: '"The booking process was so simple and the customer service was excellent. I\'ll definitely be using them again for all my business needs."',
            name: 'Kofi Mensah',
            role: 'Founder, Kofi\'s Kreations'
        },
        {
            quote: '"Great service for my small business. They handle all our deliveries with care and are always on time, which is crucial for customer satisfaction."',
            name: 'Esi Addo',
            role: 'Owner, Addo\'s Boutique'
        }
    ];

    let currentReviewIndex = 0;
    const reviewerQuote = document.getElementById('reviewer-quote');
    const reviewerName = document.getElementById('reviewer-name');
    const reviewerRole = document.getElementById('reviewer-role');
    const reviewCard = document.getElementById('review-card');
    const nextBtn = document.getElementById('next-review');
    const prevBtn = document.getElementById('prev-review');

    function showReview(index) {
        const review = reviewsData[index];
        
        if (typeof gsap !== 'undefined' && reviewCard) {
            const tl = gsap.timeline();
            tl.to([reviewCard], { 
                opacity: 0, 
                scale: 0.95, 
                duration: 0.3, 
                ease: 'power2.in' 
            })
            .call(() => {
                if (reviewerQuote) reviewerQuote.textContent = review.quote;
                if (reviewerName) reviewerName.textContent = review.name;
                if (reviewerRole) reviewerRole.textContent = review.role;
            })
            .to([reviewCard], { 
                opacity: 1, 
                scale: 1, 
                duration: 0.3, 
                ease: 'power2.out' 
            });
        } else {
            // Fallback without GSAP
            if (reviewerQuote) reviewerQuote.textContent = review.quote;
            if (reviewerName) reviewerName.textContent = review.name;
            if (reviewerRole) reviewerRole.textContent = review.role;
        }
    }

    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            currentReviewIndex = (currentReviewIndex + 1) % reviewsData.length;
            showReview(currentReviewIndex);
        });
    }

    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            currentReviewIndex = (currentReviewIndex - 1 + reviewsData.length) % reviewsData.length;
            showReview(currentReviewIndex);
        });
    }
}

// Form Enhancements
function initializeFormEnhancements() {
    // Add focus effects to form inputs
    const inputs = document.querySelectorAll('input, select, textarea');
    
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
    
    // Form validation enhancements
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const requiredFields = this.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('border-red-500');
                    
                    // Remove error styling after user starts typing
                    field.addEventListener('input', function() {
                        this.classList.remove('border-red-500');
                    }, { once: true });
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                showToast('Please fill in all required fields', 'error');
            }
        });
    });
}

// Toast Notifications
function initializeToastNotifications() {
    // This function is called from the layout file
    // Additional toast functionality can be added here
}

// Global Toast Function (called from layout)
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast-notification p-4 rounded-lg shadow-lg text-white transform transition-all duration-300 translate-x-full`;
    
    const bgColor = {
        'success': 'bg-green-500',
        'error': 'bg-red-500',
        'warning': 'bg-yellow-500',
        'info': 'bg-blue-500'
    }[type] || 'bg-blue-500';
    
    toast.classList.add(bgColor);
    toast.innerHTML = `
        <div class="flex items-center justify-between">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    const container = document.getElementById('toast-container');
    if (container) {
        container.appendChild(toast);
        
        // Animate in
        setTimeout(() => {
            toast.classList.remove('translate-x-full');
        }, 100);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            toast.classList.add('translate-x-full');
            setTimeout(() => toast.remove(), 300);
        }, 5000);
    }
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Export functions for global use
window.showToast = showToast;
