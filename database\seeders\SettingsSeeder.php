<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // Pricing Settings
            [
                'key' => 'base_cost_document',
                'value' => '15.00',
                'type' => 'number',
                'group' => 'pricing',
                'label' => 'Document Base Cost',
                'description' => 'Base cost for document deliveries',
                'is_public' => false,
            ],
            [
                'key' => 'base_cost_small',
                'value' => '20.00',
                'type' => 'number',
                'group' => 'pricing',
                'label' => 'Small Package Base Cost',
                'description' => 'Base cost for small package deliveries',
                'is_public' => false,
            ],
            [
                'key' => 'base_cost_medium',
                'value' => '35.00',
                'type' => 'number',
                'group' => 'pricing',
                'label' => 'Medium Package Base Cost',
                'description' => 'Base cost for medium package deliveries',
                'is_public' => false,
            ],
            [
                'key' => 'base_cost_large',
                'value' => '50.00',
                'type' => 'number',
                'group' => 'pricing',
                'label' => 'Large Package Base Cost',
                'description' => 'Base cost for large package deliveries',
                'is_public' => false,
            ],
            [
                'key' => 'cost_per_km',
                'value' => '2.50',
                'type' => 'number',
                'group' => 'pricing',
                'label' => 'Cost Per Kilometer',
                'description' => 'Additional cost per kilometer of distance',
                'is_public' => false,
            ],
            [
                'key' => 'cost_per_kg',
                'value' => '2.50',
                'type' => 'number',
                'group' => 'pricing',
                'label' => 'Cost Per Kilogram',
                'description' => 'Additional cost per kilogram of weight',
                'is_public' => false,
            ],
            [
                'key' => 'minimum_cost',
                'value' => '15.00',
                'type' => 'number',
                'group' => 'pricing',
                'label' => 'Minimum Delivery Cost',
                'description' => 'Minimum cost for any delivery',
                'is_public' => false,
            ],

            // Company Settings
            [
                'key' => 'name',
                'value' => 'TTAJet Courier Service',
                'type' => 'string',
                'group' => 'company',
                'label' => 'Company Name',
                'description' => 'Your company name as displayed to customers',
                'is_public' => true,
            ],
            [
                'key' => 'address',
                'value' => 'Main Office Address',
                'type' => 'string',
                'group' => 'company',
                'label' => 'Company Address',
                'description' => 'Main office address for pickup origin',
                'is_public' => false,
            ],
            [
                'key' => 'phone',
                'value' => '+1234567890',
                'type' => 'string',
                'group' => 'company',
                'label' => 'Company Phone',
                'description' => 'Main contact phone number',
                'is_public' => true,
            ],
            [
                'key' => 'email',
                'value' => '<EMAIL>',
                'type' => 'string',
                'group' => 'company',
                'label' => 'Company Email',
                'description' => 'Main contact email address',
                'is_public' => true,
            ],
            [
                'key' => 'latitude',
                'value' => '0.0',
                'type' => 'number',
                'group' => 'company',
                'label' => 'Latitude',
                'description' => 'Latitude coordinate for company location',
                'is_public' => false,
            ],
            [
                'key' => 'longitude',
                'value' => '0.0',
                'type' => 'number',
                'group' => 'company',
                'label' => 'Longitude',
                'description' => 'Longitude coordinate for company location',
                'is_public' => false,
            ],

            // Currency Settings
            [
                'key' => 'code',
                'value' => 'USD',
                'type' => 'string',
                'group' => 'currency',
                'label' => 'Currency Code',
                'description' => 'Three-letter currency code (e.g., USD, EUR)',
                'is_public' => true,
            ],
            [
                'key' => 'symbol',
                'value' => '$',
                'type' => 'string',
                'group' => 'currency',
                'label' => 'Currency Symbol',
                'description' => 'Currency symbol to display (e.g., $, €)',
                'is_public' => true,
            ],
            [
                'key' => 'position',
                'value' => 'before',
                'type' => 'string',
                'group' => 'currency',
                'label' => 'Symbol Position',
                'description' => 'Whether to show symbol before or after amount',
                'is_public' => true,
            ],

            // Miscellaneous Settings
            [
                'key' => 'timezone',
                'value' => 'UTC',
                'type' => 'string',
                'group' => 'misc',
                'label' => 'System Timezone',
                'description' => 'Default timezone for the application',
                'is_public' => true,
            ],
            [
                'key' => 'date_format',
                'value' => 'Y-m-d',
                'type' => 'string',
                'group' => 'misc',
                'label' => 'Date Format',
                'description' => 'Format for displaying dates',
                'is_public' => true,
            ],
            [
                'key' => 'time_format',
                'value' => 'H:i',
                'type' => 'string',
                'group' => 'misc',
                'label' => 'Time Format',
                'description' => 'Format for displaying times',
                'is_public' => true,
            ],
            [
                'key' => 'items_per_page',
                'value' => '20',
                'type' => 'number',
                'group' => 'misc',
                'label' => 'Items Per Page',
                'description' => 'Number of items to show per page in listings',
                'is_public' => false,
            ],
            [
                'key' => 'google_maps_api_key',
                'value' => '',
                'type' => 'string',
                'group' => 'misc',
                'label' => 'Google Maps API Key',
                'description' => 'API key for Google Maps integration',
                'is_public' => false,
            ],
            [
                'key' => 'enable_notifications',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'misc',
                'label' => 'Enable Notifications',
                'description' => 'Enable system notifications',
                'is_public' => false,
            ],
            [
                'key' => 'enable_email_notifications',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'misc',
                'label' => 'Enable Email Notifications',
                'description' => 'Enable email notifications to users',
                'is_public' => false,
            ],
            [
                'key' => 'enable_sms_notifications',
                'value' => '0',
                'type' => 'boolean',
                'group' => 'misc',
                'label' => 'Enable SMS Notifications',
                'description' => 'Enable SMS notifications to users',
                'is_public' => false,
            ],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }

        $this->command->info('Settings seeded successfully!');
    }
}
