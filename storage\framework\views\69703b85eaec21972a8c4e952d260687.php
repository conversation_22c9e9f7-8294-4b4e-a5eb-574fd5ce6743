<header class="bg-black text-white absolute top-0 w-full z-50 backdrop-blur-sm" style="margin-top: 0px !important;">
    <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
        <!-- Logo -->
        <a href="<?php echo e(route('home')); ?>" class="text-2xl font-bold cursor-pointer bg-black" style="padding: auto 30px">
            <img src="<?php echo e(asset('images/logo.jpeg')); ?>" alt="TTAJET" class="h-[75px] w-full object-cover">
        </a>
        
        <!-- Desktop Navigation -->
        <div class="hidden md:flex items-center space-x-8">
            <?php if(auth()->guard()->guest()): ?>
                <a href="<?php echo e(route('home')); ?>#about" class="text-gray-300 hover:text-white transition-colors">About</a>
                <a href="<?php echo e(route('home')); ?>#services" class="text-gray-300 hover:text-white transition-colors">Services</a>
                <a href="<?php echo e(route('booking.create')); ?>" class="text-gray-300 hover:text-white transition-colors">Book a Delivery</a>
                <a href="<?php echo e(route('tracking')); ?>" class="text-gray-300 hover:text-white transition-colors">Track Booking</a>
                <a href="<?php echo e(route('login')); ?>" class="text-gray-300 hover:text-white transition-colors">Login</a>
                <a href="<?php echo e(route('register')); ?>" class="bg-orange-600 px-5 py-2.5 rounded-lg text-sm font-semibold hover:bg-orange-700 transition-colors">Sign Up</a>
            <?php else: ?>
                <?php if(auth()->user()->isCustomer()): ?>
                    <a href="<?php echo e(route('customer.dashboard')); ?>" class="text-gray-300 hover:text-white transition-colors">Dashboard</a>
                    <a href="<?php echo e(route('booking.create')); ?>" class="text-gray-300 hover:text-white transition-colors">Book Delivery</a>
                    <a href="<?php echo e(route('booking.history')); ?>" class="text-gray-300 hover:text-white transition-colors">My Bookings</a>
                    <a href="<?php echo e(route('tracking')); ?>" class="text-gray-300 hover:text-white transition-colors">Track Booking</a>
                <?php elseif(auth()->user()->isAdmin()): ?>
                    <a href="<?php echo e(route('admin.dashboard')); ?>" class="bg-orange-600 px-5 py-2.5 rounded-lg text-sm font-semibold hover:bg-orange-700 transition-colors">Admin Dashboard</a>
                    <a href="<?php echo e(route('admin.bookings.index')); ?>" class="text-gray-300 hover:text-white transition-colors">Manage Bookings</a>
                    <a href="<?php echo e(route('admin.users.index')); ?>" class="text-gray-300 hover:text-white transition-colors">Manage Users</a>
                    <a href="<?php echo e(route('admin.settings')); ?>" class="text-gray-300 hover:text-white transition-colors">Settings</a>

                <?php endif; ?>
                
                <!-- User Dropdown -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center space-x-2 text-gray-300 hover:text-white transition-colors">
                        <i class="fas fa-user-circle text-xl"></i>
                        <span><?php echo e(auth()->user()->name); ?></span>
                        <i class="fas fa-chevron-down text-sm"></i>
                    </button>
                    
                    <div x-show="open" @click.away="open = false" 
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 scale-95"
                         x-transition:enter-end="opacity-100 scale-100"
                         x-transition:leave="transition ease-in duration-75"
                         x-transition:leave-start="opacity-100 scale-100"
                         x-transition:leave-end="opacity-0 scale-95"
                         class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                        
                        <a href="<?php echo e(route('profile.edit')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i class="fas fa-user mr-2"></i>Profile
                        </a>
                        
                        <?php if(auth()->user()->isCustomer()): ?>
                            <a href="<?php echo e(route('customer.notifications')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-bell mr-2"></i>Notifications
                            </a>
                        <?php endif; ?>
                        
                        <div class="border-t border-gray-100"></div>
                        
                        <form method="POST" action="<?php echo e(route('logout')); ?>">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-sign-out-alt mr-2"></i>Sign Out
                            </button>
                        </form>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Mobile Menu Button -->
        <div class="md:hidden">
            <button @click="mobileMenuOpen = !mobileMenuOpen" class="text-white focus:outline-none">
                <i class="fas fa-bars text-2xl"></i>
            </button>
        </div>
    </nav>
    
    <!-- Mobile Navigation Menu -->
    <div x-show="mobileMenuOpen" 
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 -translate-y-1"
         x-transition:enter-end="opacity-100 translate-y-0"
         x-transition:leave="transition ease-in duration-150"
         x-transition:leave-start="opacity-100 translate-y-0"
         x-transition:leave-end="opacity-0 -translate-y-1"
         class="md:hidden bg-black/50 backdrop-blur-sm border-t border-gray-800">
        
        <div class="px-6 py-4 space-y-4">
            <?php if(auth()->guard()->guest()): ?>
                <a href="<?php echo e(route('home')); ?>#about" class="block hover:text-orange-500 transition-colors">About</a>
                <a href="<?php echo e(route('home')); ?>#services" class="block hover:text-orange-500 transition-colors">Services</a>
                <a href="<?php echo e(route('tracking')); ?>" class="block hover:text-orange-500 transition-colors">Track Package</a>
                <a href="<?php echo e(route('booking.create')); ?>" class="block hover:text-orange-500 transition-colors">Book a Delivery</a>
                <a href="<?php echo e(route('login')); ?>" class="block hover:text-orange-500 transition-colors">Login</a>
                <a href="<?php echo e(route('register')); ?>" class="block bg-orange-500 px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors text-center">Sign Up</a>
            <?php else: ?>
                <?php if(auth()->user()->isCustomer()): ?>
                    <a href="<?php echo e(route('customer.dashboard')); ?>" class="block hover:text-orange-500 transition-colors">Dashboard</a>
                    <a href="<?php echo e(route('booking.create')); ?>" class="block hover:text-orange-500 transition-colors">Book Delivery</a>
                    <a href="<?php echo e(route('booking.history')); ?>" class="block hover:text-orange-500 transition-colors">My Bookings</a>
                    <a href="<?php echo e(route('tracking')); ?>" class="block hover:text-orange-500 transition-colors">Track Booking</a>
                <?php elseif(auth()->user()->isAdmin()): ?>
                    <a href="<?php echo e(route('admin.dashboard')); ?>" class="block hover:text-orange-500 transition-colors">Dashboard</a>
                    <a href="<?php echo e(route('admin.bookings.index')); ?>" class="block hover:text-orange-500 transition-colors">Manage Bookings</a>
                    <a href="<?php echo e(route('admin.users.index')); ?>" class="block hover:text-orange-500 transition-colors">Manage Users</a>
                    <a href="<?php echo e(route('admin.settings')); ?>" class="block hover:text-orange-500 transition-colors">Settings</a>

                <?php endif; ?>
                
                <div class="border-t border-gray-800 pt-4">
                    <a href="<?php echo e(route('profile.edit')); ?>" class="block hover:text-orange-500 transition-colors">Profile</a>
                    <form method="POST" action="<?php echo e(route('logout')); ?>" class="mt-2">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="block w-full text-left hover:text-orange-500 transition-colors">Sign Out</button>
                    </form>
                </div>
            <?php endif; ?>
        </div>
    </div>
</header>

<?php $__env->startPush('scripts'); ?>
<script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('navigation', () => ({
            mobileMenuOpen: false
        }))
    })
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\ttajetcom\resources\views/partials/navigation.blade.php ENDPATH**/ ?>