<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Branch;
use Illuminate\Http\Request;

class LiveMapController extends Controller
{
    /**
     * Show the live map deliveries view
     */
    public function index(Request $request)
    {
        $query = Booking::with(['customer']);

        // Filter by status - show only active deliveries by default
        $status = $request->get('status', 'active');
        if ($status === 'active') {
            $query->whereIn('status', ['confirmed', 'in_progress', 'delivery_enroute']);
        } elseif ($status !== 'all') {
            $query->where('status', $status);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('booking_id', 'like', "%{$search}%")
                  ->orWhere('pickup_address', 'like', "%{$search}%")
                  ->orWhere('delivery_address', 'like', "%{$search}%")
                  ->orWhereHas('customer', function($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('phone_number', 'like', "%{$search}%");
                  });
            });
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $bookings = $query->orderBy('created_at', 'desc')->get();

        // Get statistics
        $stats = [
            'total_active' => Booking::whereIn('status', ['confirmed', 'in_progress', 'delivery_enroute'])->count(),
            'pending_assignment' => Booking::where('status', 'pending')->count(),
            'in_transit' => Booking::whereIn('status', ['in_progress', 'delivery_enroute'])->count(),
            'completed_today' => Booking::where('status', 'completed')
                                      ->whereDate('updated_at', today())
                                      ->count()
        ];

        return view('live-map.index', compact('bookings', 'stats'));
    }

    /**
     * Get live tracking data for a specific booking
     */
    public function getBookingTrackingData(Booking $booking)
    {
        $booking->load(['customer']);

        return response()->json([
            'booking_id' => $booking->booking_id,
            'status' => $booking->status,
            'pickup' => [
                'address' => $booking->pickup_address,
                'latitude' => (float) $booking->pickup_latitude,
                'longitude' => (float) $booking->pickup_longitude,
                'person_name' => $booking->pickup_person_name,
                'person_phone' => $booking->pickup_person_phone
            ],
            'delivery' => [
                'address' => $booking->delivery_address,
                'latitude' => (float) $booking->delivery_latitude,
                'longitude' => (float) $booking->delivery_longitude,
                'receiver_name' => $booking->receiver_name,
                'receiver_phone' => $booking->receiver_phone
            ],
            'estimated_cost' => $booking->estimated_cost,
            'distance_km' => $booking->distance_km,
            'created_at' => $booking->created_at->format('Y-m-d H:i:s'),
            'actual_pickup_time' => $booking->actual_pickup_time?->format('Y-m-d H:i:s'),
        ]);
    }

    /**
     * Get all active deliveries for map display
     */
    public function getActiveDeliveries()
    {
        $bookings = Booking::with(['customer'])
        ->whereIn('status', ['confirmed', 'in_progress', 'delivery_enroute'])
        ->get();

        $deliveries = $bookings->map(function($booking) {
            return [
                'id' => $booking->id,
                'booking_id' => $booking->booking_id,
                'status' => $booking->status,
                'pickup' => [
                    'latitude' => (float) $booking->pickup_latitude,
                    'longitude' => (float) $booking->pickup_longitude,
                    'address' => $booking->pickup_address
                ],
                'delivery' => [
                    'latitude' => (float) $booking->delivery_latitude,
                    'longitude' => (float) $booking->delivery_longitude,
                    'address' => $booking->delivery_address
                ],
            ];
        });

        return response()->json($deliveries);
    }

    /**
     * Get all active branches for map display
     */
    public function getBranches()
    {
        $branches = Branch::select([
            'id', 'name', 'address', 'latitude', 'longitude',
            'phone', 'status', 'operating_hours'
        ])
        ->get()
        ->map(function($branch) {
            return [
                'id' => $branch->id,
                'name' => $branch->name,
                'address' => $branch->address,
                'latitude' => (float) $branch->latitude,
                'longitude' => (float) $branch->longitude,
                'phone' => $branch->phone,
                'status' => $branch->status,
                'operating_hours' => $branch->operating_hours,
                'formatted_operating_hours' => $branch->formatted_operating_hours,
            ];
        });

        return response()->json($branches);
    }


}
