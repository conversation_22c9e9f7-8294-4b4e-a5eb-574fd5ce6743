/* TTAJet Courier Service - Unified Styles */

/* Base Styles */
body {
    font-family: 'Poppins', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #F9FAFB; /* Light gray base background */
}

/* Brand Colors - Restricted to 3-color palette */
.brand-orange {
    background-color: #F97316;
}

.brand-orange-text {
    color: #F97316;
}

.brand-orange-border {
    border-color: #F97316;
}

/* Enhanced Glassmorphism Effect */
.glass-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Hero Section */
.hero-section {
    background-image: url('https://img.freepik.com/free-vector/worldwide-global-map-outline-black-background_1017-46153.jpg');
    background-size: cover;
    background-position: center;
}

/* Pricing Section */
#pricing {
    background-image: linear-gradient(to top right, #181818, #000000);
}

/* Service Cards */
.service-card {
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Price Cards */
.price-card {
    transition: all 0.3s ease;
}

.price-card:hover {
    transform: translateY(-4px);
}

/* Form Styles */
.form-input-icon {
    position: absolute;
    left: 1.25rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9CA3AF;
    pointer-events: none;
}

.form-input {
    padding-left: 3.5rem !important;
}

/* Loading Spinner */
.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #F97316;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #F97316;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #ea580c;
}

/* Navigation Styles */
header {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Button Styles */
.btn-primary {
    background-color: #F97316;
    color: white;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #ea580c;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Toast Notifications */
.toast-notification {
    min-width: 300px;
    max-width: 500px;
}

/* Responsive Design Utilities */
@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 3rem;
    }
    
    .service-card {
        margin-bottom: 2rem;
    }
    
    .price-card {
        margin-bottom: 2rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Fallback styles to ensure elements are visible - only apply to elements that exist */
.text-content > *,
.booking-form,
.service-card,
.price-card,
#about-preview *,
#reviews * {
    opacity: 1 !important;
    transform: translateY(0) scale(1) !important;
    visibility: visible !important;
}

/* Only hide elements if JavaScript is enabled and GSAP is working - and only on homepage */
body.js-animations-enabled .text-content > *,
body.js-animations-enabled .booking-form,
body.js-animations-enabled .service-card,
body.js-animations-enabled .price-card {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
}

/* Ensure booking form pages are never affected by homepage animations */
body:not(.homepage) .text-content > *,
body:not(.homepage) .booking-form,
body:not(.homepage) .service-card,
body:not(.homepage) .price-card {
    opacity: 1 !important;
    transform: translateY(0) scale(1) !important;
    visibility: visible !important;
}

/* Focus States */
input:focus, select:focus, textarea:focus {
    outline: none;
    box-shadow: 0 0 0 2px #F97316;
    border-color: #F97316;
}

/* Card Shadows */
.card-shadow {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.card-shadow-lg {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Utility Classes */
.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.backdrop-blur {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Review Section Styles */
#review-card {
    transition: all 0.3s ease;
}

/* Newsletter Section */
.newsletter-section {
    background: linear-gradient(190deg, #2222225d 0%, #00000098 100%);
}

/* Footer Enhancements */
footer {
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
}

/* Social Icons */
.social-icon {
    width: 36px;
    height: 36px;
    background-color: #202020;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.social-icon:hover {
    background-color: #F97316;
    color: white;
    transform: translateY(-2px);
}
