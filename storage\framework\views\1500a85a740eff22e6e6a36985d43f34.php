<?php $__env->startSection('title', 'System Settings - TTAJet Admin'); ?>

<?php $__env->startSection('content'); ?>

<style>
    main {
        margin-top: 0px !important;
    }
</style>

<div class="min-h-screen bg-gray-50">
    
    <!-- Header -->
    <div class="bg-white shadow-sm" style="padding-top: 12vh;">
        <div class="container mx-auto px-6 py-8">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">System Settings</h1>
                    <p class="text-gray-600 mt-1">Configure pricing, company information, and system preferences</p>
                </div>
                <div class="mt-4 md:mt-0">
                    <a href="<?php echo e(route('admin.dashboard')); ?>"
                       class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-semibold">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container mx-auto px-6 py-8">
        
        <?php if(session('success')): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                <i class="fas fa-check-circle mr-2"></i><?php echo e(session('success')); ?>

            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <i class="fas fa-exclamation-circle mr-2"></i><?php echo e(session('error')); ?>

            </div>
        <?php endif; ?>

        <?php if($errors->any()): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <div class="font-bold">Please correct the following errors:</div>
                <ul class="mt-2 list-disc list-inside">
                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li><?php echo e($error); ?></li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
        <?php endif; ?>

        <form action="<?php echo e(route('admin.settings.update')); ?>" method="POST" class="space-y-8">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PATCH'); ?>

            <!-- Pricing Settings -->
            <div class="bg-white rounded-xl shadow-sm">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-bold text-gray-900 flex items-center">
                        <i class="fas fa-dollar-sign mr-3 text-green-600"></i>
                        Pricing Configuration
                    </h3>
                    <p class="text-gray-600 mt-1">Set base costs and pricing rules for deliveries</p>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        
                        <!-- Base Costs -->
                        <div class="space-y-4">
                            <h4 class="font-semibold text-gray-900">Base Costs by Package Type</h4>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Document Delivery</label>
                                <div class="relative">
                                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-6 text-center"><?php echo e($settings['currency']['symbol'] ?? '$'); ?></span>
                                    <input type="number" step="0.01" min="0" 
                                           name="pricing[base_cost_document]" 
                                           value="<?php echo e(old('pricing.base_cost_document', $settings['pricing']['base_cost_document'] ?? 15.00)); ?>"
                                           class="pl-13 w-full border border-gray-300 rounded-lg px-16 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Small Package</label>
                                <div class="relative">
                                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-6 text-center"><?php echo e($settings['currency']['symbol'] ?? '$'); ?></span>
                                    <input type="number" step="0.01" min="0" 
                                           name="pricing[base_cost_small]" 
                                           value="<?php echo e(old('pricing.base_cost_small', $settings['pricing']['base_cost_small'] ?? 20.00)); ?>"
                                           class="pl-13 w-full border border-gray-300 rounded-lg px-16 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Medium Package</label>
                                <div class="relative">
                                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-6 text-center"><?php echo e($settings['currency']['symbol'] ?? '$'); ?></span>
                                    <input type="number" step="0.01" min="0" 
                                           name="pricing[base_cost_medium]" 
                                           value="<?php echo e(old('pricing.base_cost_medium', $settings['pricing']['base_cost_medium'] ?? 35.00)); ?>"
                                           class="pl-13 w-full border border-gray-300 rounded-lg px-16 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Large Package</label>
                                <div class="relative">
                                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-6 text-center"><?php echo e($settings['currency']['symbol'] ?? '$'); ?></span>
                                    <input type="number" step="0.01" min="0" 
                                           name="pricing[base_cost_large]" 
                                           value="<?php echo e(old('pricing.base_cost_large', $settings['pricing']['base_cost_large'] ?? 50.00)); ?>"
                                           class="pl-13 w-full border border-gray-300 rounded-lg px-16 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                                </div>
                            </div>
                        </div>

                        <!-- Additional Costs -->
                        <div class="space-y-4">
                            <h4 class="font-semibold text-gray-900">Additional Costs</h4>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Cost Per Kilometer</label>
                                <div class="relative">
                                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-6 text-center"><?php echo e($settings['currency']['symbol'] ?? '$'); ?></span>
                                    <input type="number" step="0.01" min="0" 
                                           name="pricing[cost_per_km]" 
                                           value="<?php echo e(old('pricing.cost_per_km', $settings['pricing']['cost_per_km'] ?? 2.50)); ?>"
                                           class="pl-13 w-full border border-gray-300 rounded-lg px-16 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                                </div>
                                <p class="text-xs text-gray-500 mt-1">Additional charge per kilometer of distance</p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Cost Per Kilogram</label>
                                <div class="relative">
                                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-6 text-center"><?php echo e($settings['currency']['symbol'] ?? '$'); ?></span>
                                    <input type="number" step="0.01" min="0" 
                                           name="pricing[cost_per_kg]" 
                                           value="<?php echo e(old('pricing.cost_per_kg', $settings['pricing']['cost_per_kg'] ?? 2.50)); ?>"
                                           class="pl-13 w-full border border-gray-300 rounded-lg px-16 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                                </div>
                                <p class="text-xs text-gray-500 mt-1">Additional charge per kilogram of weight</p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Minimum Delivery Cost</label>
                                <div class="relative">
                                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-6 text-center"><?php echo e($settings['currency']['symbol'] ?? '$'); ?></span>
                                    <input type="number" step="0.01" min="0" 
                                           name="pricing[minimum_cost]" 
                                           value="<?php echo e(old('pricing.minimum_cost', $settings['pricing']['minimum_cost'] ?? 15.00)); ?>"
                                           class="pl-13 w-full border border-gray-300 rounded-lg px-16 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                                </div>
                                <p class="text-xs text-gray-500 mt-1">Minimum charge for any delivery</p>
                            </div>
                        </div>

                        <!-- Pricing Preview -->
                        <div class="space-y-4">
                            <h4 class="font-semibold text-gray-900">Pricing Preview</h4>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <div class="text-sm space-y-2">
                                    <div class="flex justify-between">
                                        <span>Document (5km):</span>
                                        <span class="font-medium"><?php echo e($settings['currency']['symbol'] ?? '$'); ?><?php echo e(number_format(($settings['pricing']['base_cost_document'] ?? 15) + (5 * ($settings['pricing']['cost_per_km'] ?? 2.5)), 2)); ?></span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>Small (10km, 2kg):</span>
                                        <span class="font-medium"><?php echo e($settings['currency']['symbol'] ?? '$'); ?><?php echo e(number_format(($settings['pricing']['base_cost_small'] ?? 20) + (10 * ($settings['pricing']['cost_per_km'] ?? 2.5)) + (2 * ($settings['pricing']['cost_per_kg'] ?? 2.5)), 2)); ?></span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>Medium (15km, 5kg):</span>
                                        <span class="font-medium"><?php echo e($settings['currency']['symbol'] ?? '$'); ?><?php echo e(number_format(($settings['pricing']['base_cost_medium'] ?? 35) + (15 * ($settings['pricing']['cost_per_km'] ?? 2.5)) + (5 * ($settings['pricing']['cost_per_kg'] ?? 2.5)), 2)); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Company Information -->
            <div class="bg-white rounded-xl shadow-sm">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-bold text-gray-900 flex items-center">
                        <i class="fas fa-building mr-3 text-blue-600"></i>
                        Company Information
                    </h3>
                    <p class="text-gray-600 mt-1">Configure your company details and origin location for tracking</p>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        
                        <!-- Basic Information -->
                        <div class="space-y-4">
                            <h4 class="font-semibold text-gray-900">Basic Information</h4>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Company Name</label>
                                <input type="text" 
                                       name="company[name]" 
                                       value="<?php echo e(old('company.name', $settings['company']['name'] ?? 'TTAJet Courier Service')); ?>"
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                       required>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Company Address</label>
                                <textarea name="company[address]" rows="3"
                                          class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                          required><?php echo e(old('company.address', $settings['company']['address'] ?? 'Main Office Address')); ?></textarea>
                                <p class="text-xs text-gray-500 mt-1">This will be used as the origin point for all deliveries</p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                                <input type="tel" 
                                       name="company[phone]" 
                                       value="<?php echo e(old('company.phone', $settings['company']['phone'] ?? '+1234567890')); ?>"
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                       required>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                                <input type="email" 
                                       name="company[email]" 
                                       value="<?php echo e(old('company.email', $settings['company']['email'] ?? '<EMAIL>')); ?>"
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                       required>
                            </div>
                        </div>

                        <!-- Location Coordinates -->
                        <div class="space-y-4">
                            <h4 class="font-semibold text-gray-900">Location Coordinates</h4>
                            <p class="text-sm text-gray-600">Set the exact coordinates for your company location. This will be used as the origin point for all delivery tracking.</p>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Latitude</label>
                                <input type="number" step="0.000001" 
                                       name="company[latitude]" 
                                       value="<?php echo e(old('company.latitude', $settings['company']['latitude'] ?? 0.0)); ?>"
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                       required>
                                <p class="text-xs text-gray-500 mt-1">Example: 40.7128 (for New York)</p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Longitude</label>
                                <input type="number" step="0.000001" 
                                       name="company[longitude]" 
                                       value="<?php echo e(old('company.longitude', $settings['company']['longitude'] ?? 0.0)); ?>"
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                       required>
                                <p class="text-xs text-gray-500 mt-1">Example: -74.0060 (for New York)</p>
                            </div>

                            <div class="bg-blue-50 p-4 rounded-lg">
                                <div class="flex items-start">
                                    <i class="fas fa-info-circle text-blue-500 mt-1 mr-2"></i>
                                    <div class="text-sm text-blue-700">
                                        <strong>Tip:</strong> You can find coordinates by searching your address on Google Maps, right-clicking on the location, and selecting the coordinates that appear.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Currency Settings -->
            <div class="bg-white rounded-xl shadow-sm">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-bold text-gray-900 flex items-center">
                        <i class="fas fa-coins mr-3 text-yellow-600"></i>
                        Currency Settings
                    </h3>
                    <p class="text-gray-600 mt-1">Configure the currency used throughout the system</p>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Currency Code</label>
                            <select name="currency[code]" 
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                    required>
                                <option value="USD" <?php echo e((old('currency.code', $settings['currency']['code'] ?? 'USD') == 'USD') ? 'selected' : ''); ?>>USD - US Dollar</option>
                                <option value="EUR" <?php echo e((old('currency.code', $settings['currency']['code'] ?? 'USD') == 'EUR') ? 'selected' : ''); ?>>EUR - Euro</option>
                                <option value="GBP" <?php echo e((old('currency.code', $settings['currency']['code'] ?? 'USD') == 'GBP') ? 'selected' : ''); ?>>GBP - British Pound</option>
                                <option value="CAD" <?php echo e((old('currency.code', $settings['currency']['code'] ?? 'USD') == 'CAD') ? 'selected' : ''); ?>>CAD - Canadian Dollar</option>
                                <option value="AUD" <?php echo e((old('currency.code', $settings['currency']['code'] ?? 'USD') == 'AUD') ? 'selected' : ''); ?>>AUD - Australian Dollar</option>
                                <option value="JPY" <?php echo e((old('currency.code', $settings['currency']['code'] ?? 'USD') == 'JPY') ? 'selected' : ''); ?>>JPY - Japanese Yen</option>
                                <option value="GHS" <?php echo e((old('currency.code', $settings['currency']['code'] ?? 'USD') == 'GHS') ? 'selected' : ''); ?>>GHS - Ghanaian Cedi</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Currency Symbol</label>
                            <input type="text" 
                                   name="currency[symbol]" 
                                   value="<?php echo e(old('currency.symbol', $settings['currency']['symbol'] ?? '$')); ?>"
                                   class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                   maxlength="5"
                                   required>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Symbol Position</label>
                            <select name="currency[position]" 
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                    required>
                                <option value="before" <?php echo e((old('currency.position', $settings['currency']['position'] ?? 'before') == 'before') ? 'selected' : ''); ?>>Before amount ($100)</option>
                                <option value="after" <?php echo e((old('currency.position', $settings['currency']['position'] ?? 'before') == 'after') ? 'selected' : ''); ?>>After amount (100$)</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Miscellaneous Settings -->
            <div class="bg-white rounded-xl shadow-sm">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-bold text-gray-900 flex items-center">
                        <i class="fas fa-cogs mr-3 text-purple-600"></i>
                        Miscellaneous Settings
                    </h3>
                    <p class="text-gray-600 mt-1">Configure system preferences and integrations</p>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                        <!-- System Preferences -->
                        <div class="space-y-4">
                            <h4 class="font-semibold text-gray-900">System Preferences</h4>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">System Timezone</label>
                                <select name="misc[timezone]"
                                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                        required>
                                    <option value="UTC" <?php echo e((old('misc.timezone', $settings['misc']['timezone'] ?? 'UTC') == 'UTC') ? 'selected' : ''); ?>>UTC</option>
                                    <option value="America/New_York" <?php echo e((old('misc.timezone', $settings['misc']['timezone'] ?? 'UTC') == 'America/New_York') ? 'selected' : ''); ?>>Eastern Time (US)</option>
                                    <option value="America/Chicago" <?php echo e((old('misc.timezone', $settings['misc']['timezone'] ?? 'UTC') == 'America/Chicago') ? 'selected' : ''); ?>>Central Time (US)</option>
                                    <option value="Africa/Accra" <?php echo e((old('misc.timezone', $settings['misc']['timezone'] ?? 'UTC') == 'Africa/Accra') ? 'selected' : ''); ?>>Ghana (GMT)</option>
                                    <option value="Africa/Lagos" <?php echo e((old('misc.timezone', $settings['misc']['timezone'] ?? 'UTC') == 'Africa/Lagos') ? 'selected' : ''); ?>>Nigeria (WAT)</option>
                                    <option value="Africa/Dakar" <?php echo e((old('misc.timezone', $settings['misc']['timezone'] ?? 'UTC') == 'Africa/Dakar') ? 'selected' : ''); ?>>West Africa (GMT)</option>
                                    <option value="America/Denver" <?php echo e((old('misc.timezone', $settings['misc']['timezone'] ?? 'UTC') == 'America/Denver') ? 'selected' : ''); ?>>Mountain Time (US)</option>
                                    <option value="America/Los_Angeles" <?php echo e((old('misc.timezone', $settings['misc']['timezone'] ?? 'UTC') == 'America/Los_Angeles') ? 'selected' : ''); ?>>Pacific Time (US)</option>
                                    <option value="Europe/London" <?php echo e((old('misc.timezone', $settings['misc']['timezone'] ?? 'UTC') == 'Europe/London') ? 'selected' : ''); ?>>London</option>
                                    <option value="Europe/Paris" <?php echo e((old('misc.timezone', $settings['misc']['timezone'] ?? 'UTC') == 'Europe/Paris') ? 'selected' : ''); ?>>Paris</option>
                                    <option value="Asia/Tokyo" <?php echo e((old('misc.timezone', $settings['misc']['timezone'] ?? 'UTC') == 'Asia/Tokyo') ? 'selected' : ''); ?>>Tokyo</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Date Format</label>
                                <select name="misc[date_format]"
                                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                        required>
                                    <option value="Y-m-d" <?php echo e((old('misc.date_format', $settings['misc']['date_format'] ?? 'Y-m-d') == 'Y-m-d') ? 'selected' : ''); ?>>YYYY-MM-DD (2024-01-15)</option>
                                    <option value="m/d/Y" <?php echo e((old('misc.date_format', $settings['misc']['date_format'] ?? 'Y-m-d') == 'm/d/Y') ? 'selected' : ''); ?>>MM/DD/YYYY (01/15/2024)</option>
                                    <option value="d/m/Y" <?php echo e((old('misc.date_format', $settings['misc']['date_format'] ?? 'Y-m-d') == 'd/m/Y') ? 'selected' : ''); ?>>DD/MM/YYYY (15/01/2024)</option>
                                    <option value="M j, Y" <?php echo e((old('misc.date_format', $settings['misc']['date_format'] ?? 'Y-m-d') == 'M j, Y') ? 'selected' : ''); ?>>Jan 15, 2024</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Time Format</label>
                                <select name="misc[time_format]"
                                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                        required>
                                    <option value="H:i" <?php echo e((old('misc.time_format', $settings['misc']['time_format'] ?? 'H:i') == 'H:i') ? 'selected' : ''); ?>>24-hour (14:30)</option>
                                    <option value="g:i A" <?php echo e((old('misc.time_format', $settings['misc']['time_format'] ?? 'H:i') == 'g:i A') ? 'selected' : ''); ?>>12-hour (2:30 PM)</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Items Per Page</label>
                                <select name="misc[items_per_page]"
                                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                        required>
                                    <option value="10" <?php echo e((old('misc.items_per_page', $settings['misc']['items_per_page'] ?? 20) == 10) ? 'selected' : ''); ?>>10</option>
                                    <option value="20" <?php echo e((old('misc.items_per_page', $settings['misc']['items_per_page'] ?? 20) == 20) ? 'selected' : ''); ?>>20</option>
                                    <option value="50" <?php echo e((old('misc.items_per_page', $settings['misc']['items_per_page'] ?? 20) == 50) ? 'selected' : ''); ?>>50</option>
                                    <option value="100" <?php echo e((old('misc.items_per_page', $settings['misc']['items_per_page'] ?? 20) == 100) ? 'selected' : ''); ?>>100</option>
                                </select>
                                <p class="text-xs text-gray-500 mt-1">Number of items to show per page in admin listings</p>
                            </div>
                        </div>

                        <!-- Integrations & Notifications -->
                        <div class="space-y-4">
                            <h4 class="font-semibold text-gray-900">Integrations & Notifications</h4>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Google Maps API Key</label>
                                <input type="text"
                                       name="misc[google_maps_api_key]"
                                       value="<?php echo e(old('misc.google_maps_api_key', $settings['misc']['google_maps_api_key'] ?? '')); ?>"
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                       placeholder="Enter your Google Maps API key">
                                <p class="text-xs text-gray-500 mt-1">Required for distance calculation and mapping features</p>
                            </div>

                            <div class="space-y-3">
                                <h5 class="font-medium text-gray-900">Notification Settings</h5>

                                <div class="flex items-center">
                                    <input type="hidden" name="misc[enable_notifications]" value="0">
                                    <input type="checkbox"
                                           name="misc[enable_notifications]"
                                           value="1"
                                           <?php echo e(old('misc.enable_notifications', $settings['misc']['enable_notifications'] ?? true) ? 'checked' : ''); ?>

                                           class="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded">
                                    <label class="ml-2 block text-sm text-gray-700">Enable System Notifications</label>
                                </div>

                                <div class="flex items-center">
                                    <input type="hidden" name="misc[enable_email_notifications]" value="0">
                                    <input type="checkbox"
                                           name="misc[enable_email_notifications]"
                                           value="1"
                                           <?php echo e(old('misc.enable_email_notifications', $settings['misc']['enable_email_notifications'] ?? true) ? 'checked' : ''); ?>

                                           class="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded">
                                    <label class="ml-2 block text-sm text-gray-700">Enable Email Notifications</label>
                                </div>

                                <div class="flex items-center">
                                    <input type="hidden" name="misc[enable_sms_notifications]" value="0">
                                    <input type="checkbox"
                                           name="misc[enable_sms_notifications]"
                                           value="1"
                                           <?php echo e(old('misc.enable_sms_notifications', $settings['misc']['enable_sms_notifications'] ?? false) ? 'checked' : ''); ?>

                                           class="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded">
                                    <label class="ml-2 block text-sm text-gray-700">Enable SMS Notifications</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Save Button -->
            <div class="flex justify-end">
                <button type="submit"
                        class="brand-orange text-white px-8 py-3 rounded-lg hover:bg-orange-600 transition-colors font-semibold">
                    <i class="fas fa-save mr-2"></i>Save Settings
                </button>
            </div>

        </form>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\ttajetcom\resources\views/admin/settings/index.blade.php ENDPATH**/ ?>