<?php $__env->startSection('title', 'Booking Details - Admin'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50">
    
    <!-- Header -->
    <div class="bg-white shadow-sm">
        <div class="container mx-auto px-6 py-8">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Booking Details</h1>
                    <p class="text-gray-600 mt-1"><?php echo e($booking->booking_id); ?></p>
                </div>
                <div class="mt-4 md:mt-0 flex space-x-4">
                    <a href="<?php echo e(route('admin.bookings.index')); ?>" 
                       class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-semibold">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Bookings
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-6 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Booking Details -->
            <div class="lg:col-span-2 space-y-6">

                <!-- Live Tracking Map -->
                <?php if(in_array($booking->status, ['confirmed', 'in_progress'])): ?>
                    <div class="bg-white rounded-xl shadow-sm">
                        <div class="p-6 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg font-bold text-gray-900">
                                        <i class="fas fa-map-marker-alt text-orange-600 mr-2"></i>
                                        Live Tracking
                                    </h3>
                                    <p class="text-sm text-gray-600 mt-1">Real-time delivery monitoring</p>
                                </div>
                                <div class="flex items-center space-x-4">

                                    <button onclick="refreshAdminTracking()"
                                            class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                        <i class="fas fa-sync-alt mr-1" id="admin-refresh-icon"></i>
                                        Refresh
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="p-0">
                            <?php if (isset($component)) { $__componentOriginalbd8dc289ba51619dbf13cb5d6fad94e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbd8dc289ba51619dbf13cb5d6fad94e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.live-tracking-card','data' => ['booking' => $booking,'height' => '450px','showDetails' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('live-tracking-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['booking' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($booking),'height' => '450px','showDetails' => true]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbd8dc289ba51619dbf13cb5d6fad94e9)): ?>
<?php $attributes = $__attributesOriginalbd8dc289ba51619dbf13cb5d6fad94e9; ?>
<?php unset($__attributesOriginalbd8dc289ba51619dbf13cb5d6fad94e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbd8dc289ba51619dbf13cb5d6fad94e9)): ?>
<?php $component = $__componentOriginalbd8dc289ba51619dbf13cb5d6fad94e9; ?>
<?php unset($__componentOriginalbd8dc289ba51619dbf13cb5d6fad94e9); ?>
<?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Booking Information -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">Booking Information</h3>
                    </div>
                    <div class="p-6">
                        <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Booking ID</dt>
                                <dd class="mt-1 text-sm text-gray-900"><?php echo e($booking->booking_id); ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Status</dt>
                                <dd class="mt-1">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        <?php switch($booking->status):
                                            case ('pending'): ?>
                                                bg-yellow-100 text-yellow-800
                                                <?php break; ?>
                                            <?php case ('confirmed'): ?>
                                                bg-blue-100 text-blue-800
                                                <?php break; ?>

                                            <?php case ('in_progress'): ?>
                                                bg-purple-100 text-purple-800
                                                <?php break; ?>
                                            <?php case ('completed'): ?>
                                                bg-green-100 text-green-800
                                                <?php break; ?>
                                            <?php case ('cancelled'): ?>
                                                bg-red-100 text-red-800
                                                <?php break; ?>
                                            <?php default: ?>
                                                bg-gray-100 text-gray-800
                                        <?php endswitch; ?>
                                    ">
                                        <?php echo e(ucfirst(str_replace('_', ' ', $booking->status))); ?>

                                    </span>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Package Type</dt>
                                <dd class="mt-1 text-sm text-gray-900"><?php echo e(ucfirst($booking->package_type)); ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Package Weight</dt>
                                <dd class="mt-1 text-sm text-gray-900"><?php echo e($booking->package_weight ?? 'N/A'); ?> kg</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Distance</dt>
                                <dd class="mt-1 text-sm text-gray-900"><?php echo e($booking->distance_km ?? 'N/A'); ?> km</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Estimated Cost</dt>
                                <dd class="mt-1 text-sm text-gray-900"><?php echo e(\App\Models\Setting::formatCurrency($booking->estimated_cost)); ?></dd>
                            </div>
                            <?php if($booking->final_cost): ?>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Final Cost</dt>
                                    <dd class="mt-1 text-sm text-gray-900"><?php echo e(\App\Models\Setting::formatCurrency($booking->final_cost)); ?></dd>
                                </div>
                            <?php endif; ?>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Payment Method</dt>
                                <dd class="mt-1 text-sm text-gray-900"><?php echo e(ucfirst($booking->payment_method)); ?></dd>
                            </div>
                        </dl>
                        
                        <?php if($booking->package_description): ?>
                            <div class="mt-6">
                                <dt class="text-sm font-medium text-gray-500">Package Description</dt>
                                <dd class="mt-1 text-sm text-gray-900"><?php echo e($booking->package_description); ?></dd>
                            </div>
                        <?php endif; ?>
                        
                        <?php if($booking->special_instructions): ?>
                            <div class="mt-6">
                                <dt class="text-sm font-medium text-gray-500">Special Instructions</dt>
                                <dd class="mt-1 text-sm text-gray-900"><?php echo e($booking->special_instructions); ?></dd>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Pickup & Delivery Details -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">Pickup & Delivery Details</h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Pickup Details -->
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Pickup Information</h4>
                                <dl class="space-y-2">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Address</dt>
                                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($booking->pickup_address); ?></dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Contact Person</dt>
                                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($booking->pickup_person_name); ?></dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Phone</dt>
                                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($booking->pickup_person_phone); ?></dd>
                                    </div>
                                    <?php if($booking->scheduled_pickup_time): ?>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Scheduled Time</dt>
                                            <dd class="mt-1 text-sm text-gray-900"><?php echo e($booking->scheduled_pickup_time->format('M d, Y \a\t g:i A')); ?></dd>
                                        </div>
                                    <?php endif; ?>
                                </dl>
                            </div>
                            
                            <!-- Delivery Details -->
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Delivery Information</h4>
                                <dl class="space-y-2">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Address</dt>
                                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($booking->delivery_address); ?></dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Receiver Name</dt>
                                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($booking->receiver_name); ?></dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Phone</dt>
                                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($booking->receiver_phone); ?></dd>
                                    </div>
                                    <?php if($booking->delivered_at): ?>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Delivered At</dt>
                                            <dd class="mt-1 text-sm text-gray-900"><?php echo e($booking->delivered_at->format('M d, Y \a\t g:i A')); ?></dd>
                                        </div>
                                    <?php endif; ?>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Customer Information -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">Customer</h3>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-12 w-12">
                                <div class="h-12 w-12 rounded-full bg-indigo-500 flex items-center justify-center">
                                    <span class="text-lg font-medium text-white">
                                        <?php echo e(substr($booking->customer->name, 0, 1)); ?>

                                    </span>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900"><?php echo e($booking->customer->name); ?></div>
                                <div class="text-sm text-gray-500"><?php echo e($booking->customer->email); ?></div>
                                <?php if($booking->customer->phone_number): ?>
                                    <div class="text-sm text-gray-500"><?php echo e($booking->customer->phone_number); ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>



                <!-- Status Update -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">Update Status</h3>
                    </div>
                    <div class="p-6">
                        <form action="<?php echo e(route('admin.bookings.update-status', $booking)); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PATCH'); ?>
                            <div class="space-y-4">
                                <div>
                                    <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                                    <select name="status" id="status" required 
                                            class="mt-1 block w-full border border-gray-300 rounded-lg px-3 py-2">
                                        <option value="pending" <?php echo e($booking->status == 'pending' ? 'selected' : ''); ?>>Pending</option>
                                        <option value="confirmed" <?php echo e($booking->status == 'confirmed' ? 'selected' : ''); ?>>Confirmed</option>

                                        <option value="in_progress" <?php echo e($booking->status == 'in_progress' ? 'selected' : ''); ?>>In Progress</option>
                                        <option value="delivered" <?php echo e($booking->status == 'delivered' ? 'selected' : ''); ?>>Delivered</option>
                                        <option value="cancelled" <?php echo e($booking->status == 'cancelled' ? 'selected' : ''); ?>>Cancelled</option>
                                    </select>
                                </div>
                                
                                <div id="cancellation_reason_div" style="display: none;">
                                    <label for="cancellation_reason" class="block text-sm font-medium text-gray-700">Cancellation Reason</label>
                                    <textarea name="cancellation_reason" id="cancellation_reason" rows="3" 
                                              class="mt-1 block w-full border border-gray-300 rounded-lg px-3 py-2"
                                              placeholder="Enter reason for cancellation..."></textarea>
                                </div>
                                
                                <button type="submit" 
                                        class="w-full brand-orange text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors">
                                    Update Status
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('status').addEventListener('change', function() {
    const cancellationDiv = document.getElementById('cancellation_reason_div');
    if (this.value === 'cancelled') {
        cancellationDiv.style.display = 'block';
    } else {
        cancellationDiv.style.display = 'none';
    }
});

// Auto-refresh for active bookings (Admin view)
<?php if(in_array($booking->status, ['confirmed', 'in_progress'])): ?>
    let adminAutoRefreshInterval;

    function startAdminAutoRefresh() {
        adminAutoRefreshInterval = setInterval(() => {
            // Refresh the live tracking map if it exists
            if (typeof refreshTrackingMap<?php echo e($booking->id); ?> === 'function') {
                refreshTrackingMap<?php echo e($booking->id); ?>();
            }
        }, 30000); // Refresh every 30 seconds
    }

    function stopAdminAutoRefresh() {
        if (adminAutoRefreshInterval) {
            clearInterval(adminAutoRefreshInterval);
            adminAutoRefreshInterval = null;
        }
    }

    // Start auto-refresh when page loads
    document.addEventListener('DOMContentLoaded', function() {
        startAdminAutoRefresh();
    });

    // Stop auto-refresh when page is hidden
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            stopAdminAutoRefresh();
        } else {
            startAdminAutoRefresh();
        }
    });

    // Stop auto-refresh when user leaves the page
    window.addEventListener('beforeunload', function() {
        stopAdminAutoRefresh();
    });
<?php endif; ?>

function refreshAdminTracking() {
    const refreshIcon = document.getElementById('admin-refresh-icon');

    // Add spinning animation
    refreshIcon.classList.add('fa-spin');

    // Refresh the live tracking map
    if (typeof refreshTrackingMap<?php echo e($booking->id); ?> === 'function') {
        refreshTrackingMap<?php echo e($booking->id); ?>();
    }

    // Remove spinning animation after 2 seconds
    setTimeout(() => {
        refreshIcon.classList.remove('fa-spin');
    }, 2000);
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\ttajetcom\resources\views/admin/bookings/show.blade.php ENDPATH**/ ?>