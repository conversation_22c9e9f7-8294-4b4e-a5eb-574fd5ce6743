<?php

namespace App\Services;

use App\Models\Setting;

class CostCalculationService
{
    /**
     * Base costs for different package types (fallback values)
     */
    protected $baseCosts = [
        'document' => 15.00,
        'small' => 20.00,
        'medium' => 35.00,
        'large' => 50.00,
    ];

    /**
     * Cost per kilometer (fallback value)
     */
    protected $costPerKm = 2.50;

    /**
     * Cost per kilogram for weight (fallback value)
     */
    protected $costPerKg = 2.50;

    /**
     * Minimum delivery cost (fallback value)
     */
    protected $minimumCost = 15.00;

    /**
     * Get base costs from settings or fallback values
     */
    protected function getBaseCosts(): array
    {
        return [
            'document' => Setting::get('base_cost_document', $this->baseCosts['document']),
            'small' => Setting::get('base_cost_small', $this->baseCosts['small']),
            'medium' => Setting::get('base_cost_medium', $this->baseCosts['medium']),
            'large' => Setting::get('base_cost_large', $this->baseCosts['large']),
        ];
    }

    /**
     * Get cost per kilometer from settings or fallback value
     */
    protected function getCostPerKm(): float
    {
        return Setting::get('cost_per_km', $this->costPerKm);
    }

    /**
     * Get cost per kilogram from settings or fallback value
     */
    protected function getCostPerKg(): float
    {
        return Setting::get('cost_per_kg', $this->costPerKg);
    }

    /**
     * Get minimum cost from settings or fallback value
     */
    protected function getMinimumCost(): float
    {
        return Setting::get('minimum_cost', $this->minimumCost);
    }

    /**
     * Calculate the estimated cost for a delivery
     *
     * @param string $packageType
     * @param float $weight
     * @param float $distanceKm
     * @return float
     */
    public function calculateCost(string $packageType, float $weight = 0, float $distanceKm = 0): float
    {
        // Get base costs from settings
        $baseCosts = $this->getBaseCosts();
        $baseCost = $baseCosts[$packageType] ?? $baseCosts['small'];

        // Add distance cost
        $distanceCost = $distanceKm * $this->getCostPerKm();

        // Add weight cost
        $weightCost = $weight * $this->getCostPerKg();

        // Calculate total
        $totalCost = $baseCost + $distanceCost + $weightCost;

        // Apply minimum cost
        $totalCost = max($totalCost, $this->getMinimumCost());

        // Round to 2 decimal places
        return round($totalCost, 2);
    }

    /**
     * Get cost breakdown for transparency
     *
     * @param string $packageType
     * @param float $weight
     * @param float $distanceKm
     * @return array
     */
    public function getCostBreakdown(string $packageType, float $weight = 0, float $distanceKm = 0): array
    {
        // Get costs from settings
        $baseCosts = $this->getBaseCosts();
        $baseCost = $baseCosts[$packageType] ?? $baseCosts['small'];
        $costPerKm = $this->getCostPerKm();
        $costPerKg = $this->getCostPerKg();
        $minimumCost = $this->getMinimumCost();

        $distanceCost = $distanceKm * $costPerKm;
        $weightCost = $weight * $costPerKg;
        $subtotal = $baseCost + $distanceCost + $weightCost;
        $total = max($subtotal, $minimumCost);

        return [
            'base_cost' => $baseCost,
            'distance_cost' => $distanceCost,
            'weight_cost' => $weightCost,
            'subtotal' => $subtotal,
            'minimum_applied' => $total > $subtotal,
            'total' => $total,
            'breakdown' => [
                'Package Type (' . ucfirst($packageType) . ')' => $baseCost,
                'Distance (' . $distanceKm . ' km)' => $distanceCost,
                'Weight (' . $weight . ' kg)' => $weightCost,
            ]
        ];
    }

    /**
     * Update pricing configuration
     *
     * @param array $config
     * @return void
     */
    public function updatePricing(array $config): void
    {
        if (isset($config['base_costs'])) {
            $this->baseCosts = array_merge($this->baseCosts, $config['base_costs']);
        }

        if (isset($config['cost_per_km'])) {
            $this->costPerKm = $config['cost_per_km'];
        }

        if (isset($config['cost_per_kg'])) {
            $this->costPerKg = $config['cost_per_kg'];
        }

        if (isset($config['minimum_cost'])) {
            $this->minimumCost = $config['minimum_cost'];
        }
    }

    /**
     * Get current pricing configuration
     *
     * @return array
     */
    public function getPricingConfig(): array
    {
        return [
            'base_costs' => $this->getBaseCosts(),
            'cost_per_km' => $this->getCostPerKm(),
            'cost_per_kg' => $this->getCostPerKg(),
            'minimum_cost' => $this->getMinimumCost(),
        ];
    }
}
