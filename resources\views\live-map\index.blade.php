@extends('layouts.app')

@section('title', 'Live Map - Delivery Tracking')

@push('styles')
<style>
    /* Ensure the map container has a defined size */
    #map {
        height: 100%;
        width: 100%;
        min-height: 600px; /* Ensure a minimum height */
    }
    /* Custom InfoWindow styling */
    .gm-style-iw {
        max-width: 300px;
        padding: 0 !important;
    }
    .gm-style-iw-c {
        border-radius: 8px !important;
        padding: 0 !important;
    }
    .gm-style-iw-d {
        overflow: hidden !important;
    }
</style>
@endpush

@section('content')
<div class="min-h-screen bg-gray-50">
    
    <!-- Header -->
    <div class="bg-white shadow-sm">
        <div class="container mx-auto px-6 py-6">
            <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Live Delivery Map</h1>
                    <p class="text-gray-600 mt-1">Real-time tracking of all active deliveries</p>
                </div>
                
                <!-- Controls -->
                <div class="mt-4 lg:mt-0 flex flex-wrap gap-4">
                    <!-- Search -->
                    <div class="relative">
                        <input type="text" id="searchInput" placeholder="Search bookings, addresses..."
                               class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>
                    
                    <!-- Status Filter -->
                    <select id="statusFilter" class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                        <option value="active">Active Deliveries</option>
                        <option value="all">All Deliveries</option>
                        <option value="pending">Pending Assignment</option>
                        <option value="confirmed">Confirmed</option>
                        <option value="delivery_enroute">Delivery Enroute</option>
                        <option value="in_progress">In Progress</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                    
                    <!-- Refresh Button -->
                    <button onclick="refreshMap()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-sync-alt mr-2"></i>Refresh
                    </button>
                    
                    <!-- Auto-refresh Toggle -->
                    <label class="flex items-center">
                        <input type="checkbox" id="autoRefresh" checked class="mr-2 h-4 w-4 text-orange-600 rounded focus:ring-orange-500">
                        <span class="text-sm text-gray-700">Auto-refresh</span>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-6 py-6">
        
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-orange-100">
                        <i class="fas fa-truck text-orange-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Active Deliveries</p>
                        <p class="text-2xl font-bold text-gray-900" id="activeCount">{{ $stats['total_active'] }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Pending Assignment</p>
                        <p class="text-2xl font-bold text-gray-900" id="pendingCount">{{ $stats['pending_assignment'] }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100">
                        <i class="fas fa-route text-purple-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">In Transit</p>
                        <p class="text-2xl font-bold text-gray-900" id="transitCount">{{ $stats['in_transit'] }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Completed Today</p>
                        <p class="text-2xl font-bold text-gray-900" id="completedCount">{{ $stats['completed_today'] }}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            
            <!-- Map Container -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-bold text-gray-900">Live Delivery Map</h3>
                            <div class="flex items-center space-x-2">
                                <div id="map-status" class="flex items-center text-xs font-medium">
                                    <i class="fas fa-spinner fa-spin mr-2 text-blue-500"></i>
                                    <span class="text-gray-600">Loading map...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="relative">
                        <div id="map" style="height: 600px; width: 100%;"></div>

                        <!-- Loading Overlay -->
                        <div id="map-loading" class="absolute inset-0 bg-gray-100 flex items-center justify-center">
                            <div class="text-center">
                                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
                                <p class="text-gray-600 font-medium">Initializing map...</p>
                                <p class="text-sm text-gray-500 mt-1">Please wait while we load the delivery data</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Delivery List -->
            <div class="bg-white rounded-xl shadow-sm">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-bold text-gray-900">Active Deliveries</h3>
                    <p class="text-sm text-gray-600" id="deliveryCount">{{ count($bookings) }} deliveries found</p>
                </div>
                <div class="max-h-[550px] overflow-y-auto" id="deliveryList">
                    @forelse($bookings as $booking)
                        <div class="p-4 border-b border-gray-100 last:border-b-0 hover:bg-orange-50 cursor-pointer delivery-item transition-colors" 
                             data-booking-id="{{ $booking->id }}"
                             onclick="focusOnDelivery({{ $booking->id }})">
                            <div class="flex items-start justify-between">
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center mb-2">
                                        <span class="text-sm font-bold text-gray-900">{{ $booking->booking_id }}</span>
                                        <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                                            @switch($booking->status)
                                                @case('confirmed') bg-blue-100 text-blue-800 @break
                                                @case('in_progress') bg-purple-100 text-purple-800 @break
                                                @case('delivery_enroute') bg-orange-100 text-orange-800 @break
                                                @case('completed') bg-green-100 text-green-800 @break
                                                @case('cancelled') bg-red-100 text-red-800 @break
                                                @default bg-gray-100 text-gray-800
                                            @endswitch
                                        ">
                                            {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                        </span>
                                    </div>
                                    <div class="space-y-1">
                                        <div class="flex items-center text-xs text-gray-600">
                                            <i class="fas fa-map-marker-alt text-green-500 w-4 text-center mr-2"></i>
                                            <span class="truncate">{{ $booking->pickup_address }}</span>
                                        </div>
                                        <div class="flex items-center text-xs text-gray-600">
                                            <i class="fas fa-flag-checkered text-red-500 w-4 text-center mr-2"></i>
                                            <span class="truncate">{{ $booking->delivery_address }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="ml-2 flex-shrink-0">
                                    <button onclick="event.stopPropagation(); viewBookingDetails({{ $booking->id }})" 
                                            class="text-gray-400 hover:text-orange-600 transition-colors p-2 rounded-full hover:bg-orange-100">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="p-6 text-center text-gray-500">
                            <i class="fas fa-box-open text-3xl mb-2 text-gray-400"></i>
                            <p>No active deliveries found.</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Booking Details Modal -->
<div id="bookingModal" class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center hidden z-50 transition-opacity">
    <div class="relative mx-auto p-6 border bg-white w-11/12 max-w-2xl shadow-lg rounded-xl transform transition-all" id="modal-panel">
        <div class="flex justify-between items-center pb-3 border-b">
            <h3 class="text-xl font-bold text-gray-900" id="modalTitle">Booking Details</h3>
            <button onclick="closeBookingModal()" class="text-gray-400 hover:text-gray-600 p-2 rounded-full hover:bg-gray-100">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div id="modalContent" class="mt-4">
            <!-- Content will be loaded here -->
            <div class="text-center py-8"><i class="fas fa-spinner fa-spin text-3xl text-orange-500"></i></div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google_maps.api_key') }}&libraries=places&callback=initLiveMap" async defer></script>

<script>
// Global variables for Live Map
let liveMap;
let directionsService;
let directionsRenderers = [];
let markers = [];
let infoWindows = [];
let autoRefreshTimer;
let deliveriesData = [];

// Initialize Live Map when Google Maps API is loaded
window.initLiveMap = function() {
    console.log('🗺️ Google Maps API loaded for Live Map');

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeLiveMapComponents);
    } else {
        initializeLiveMapComponents();
    }
};

function initializeLiveMapComponents() {
    if (typeof google === 'undefined' || !google.maps) {
        console.warn('Google Maps API not loaded yet');
        return;
    }

    updateMapStatus('loading', 'Initializing map...');

    // Initialize map
    const mapElement = document.getElementById('map');
    if (mapElement) {
        liveMap = new google.maps.Map(mapElement, {
            zoom: 12,
            center: { lat: 5.6037, lng: -0.1870 }, // Accra, Ghana
            mapTypeId: google.maps.MapTypeId.ROADMAP,
            styles: [
                {
                    featureType: 'poi',
                    elementType: 'labels',
                    stylers: [{ visibility: 'off' }]
                }
            ],
            mapTypeControl: false,
            streetViewControl: false,
            fullscreenControl: true,
            zoomControlOptions: {
                position: google.maps.ControlPosition.RIGHT_BOTTOM,
            }
        });

        directionsService = new google.maps.DirectionsService();

        // Hide loading overlay once map is ready
        google.maps.event.addListenerOnce(liveMap, 'idle', function() {
            hideMapLoading();
            updateMapStatus('ready', 'Map ready');
        });

        // Load initial data
        loadDeliveryData();
        setupEventListeners();
        startAutoRefresh();
    }
}

// Load delivery data from server
async function loadDeliveryData() {
    updateMapStatus('loading', 'Loading deliveries...');

    try {
        const response = await fetch("{{ route('api.live-map.deliveries') }}", {
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        deliveriesData = await response.json();
        console.log('📦 Loaded deliveries:', deliveriesData.length);

        if (deliveriesData.length === 0) {
            updateMapStatus('ready', 'No active deliveries');
            return;
        }

        // Clear existing map elements
        clearMapElements();

        // Render all deliveries
        renderAllDeliveries();

        updateMapStatus('ready', `${deliveriesData.length} deliveries displayed`);

    } catch (error) {
        console.error('❌ Error loading delivery data:', error);
        updateMapStatus('error', 'Failed to load deliveries');
    }
}

// Render all deliveries on the map
function renderAllDeliveries() {
    if (!deliveriesData || deliveriesData.length === 0) return;

    const bounds = new google.maps.LatLngBounds();

    deliveriesData.forEach((delivery, index) => {
        // Create a unique directions renderer for each delivery
        const directionsRenderer = new google.maps.DirectionsRenderer({
            map: liveMap,
            suppressMarkers: true,
            polylineOptions: {
                strokeColor: getStatusColor(delivery.status),
                strokeWeight: 4,
                strokeOpacity: 0.8
            }
        });

        directionsRenderers.push(directionsRenderer);

        // Calculate and display route
        const request = {
            origin: { lat: delivery.pickup.latitude, lng: delivery.pickup.longitude },
            destination: { lat: delivery.delivery.latitude, lng: delivery.delivery.longitude },
            travelMode: google.maps.TravelMode.DRIVING
        };

        directionsService.route(request, (result, status) => {
            if (status === 'OK') {
                directionsRenderer.setDirections(result);
            } else {
                console.warn(`Failed to calculate route for ${delivery.booking_id}:`, status);
            }
        });

        // Add markers
        addDeliveryMarkers(delivery);

        // Extend bounds
        bounds.extend({ lat: delivery.pickup.latitude, lng: delivery.pickup.longitude });
        bounds.extend({ lat: delivery.delivery.latitude, lng: delivery.delivery.longitude });
    });

    // Fit map to show all deliveries
    if (deliveriesData.length > 0) {
        liveMap.fitBounds(bounds);
    }
}

// Add pickup and delivery markers for a delivery
function addDeliveryMarkers(delivery) {
    // Pickup marker
    const pickupMarker = new google.maps.Marker({
        position: { lat: delivery.pickup.latitude, lng: delivery.pickup.longitude },
        map: liveMap,
        title: `Pickup: ${delivery.pickup.address}`,
        icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="10" fill="#10B981" stroke="white" stroke-width="2"/>
                    <path d="M8 12l2 2 4-4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            `),
            scaledSize: new google.maps.Size(32, 32)
        }
    });

    // Delivery marker
    const deliveryMarker = new google.maps.Marker({
        position: { lat: delivery.delivery.latitude, lng: delivery.delivery.longitude },
        map: liveMap,
        title: `Delivery: ${delivery.delivery.address}`,
        icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="10" fill="#EF4444" stroke="white" stroke-width="2"/>
                    <path d="M12 8v4l3 3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            `),
            scaledSize: new google.maps.Size(32, 32)
        }
    });

    // Info windows
    const pickupInfoWindow = new google.maps.InfoWindow({
        content: `
            <div class="p-3">
                <h4 class="font-bold text-green-600">Pickup Location</h4>
                <p class="text-sm text-gray-700 mt-1">${delivery.pickup.address}</p>
                <p class="text-xs text-gray-500 mt-2">Booking: <span class="font-semibold">${delivery.booking_id}</span></p>
                <p class="text-xs text-gray-500">Status: <span class="font-semibold">${delivery.status.replace('_', ' ')}</span></p>
            </div>
        `
    });

    const deliveryInfoWindow = new google.maps.InfoWindow({
        content: `
            <div class="p-3">
                <h4 class="font-bold text-red-600">Delivery Location</h4>
                <p class="text-sm text-gray-700 mt-1">${delivery.delivery.address}</p>
                <p class="text-xs text-gray-500 mt-2">Booking: <span class="font-semibold">${delivery.booking_id}</span></p>
                <p class="text-xs text-gray-500">Status: <span class="font-semibold">${delivery.status.replace('_', ' ')}</span></p>
            </div>
        `
    });

    // Add click listeners
    pickupMarker.addListener('click', () => {
        closeAllInfoWindows();
        pickupInfoWindow.open(liveMap, pickupMarker);
        infoWindows.push(pickupInfoWindow);
    });

    deliveryMarker.addListener('click', () => {
        closeAllInfoWindows();
        deliveryInfoWindow.open(liveMap, deliveryMarker);
        infoWindows.push(deliveryInfoWindow);
    });

    markers.push(pickupMarker, deliveryMarker);
}

// Setup event listeners
function setupEventListeners() {
    // Search functionality
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', (e) => filterDeliveryList(e.target.value));
    }

    // Status filter
    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        statusFilter.addEventListener('change', (e) => {
            const url = new URL(window.location);
            url.searchParams.set('status', e.target.value);
            window.location.href = url.toString();
        });
    }

    // Auto-refresh toggle
    const autoRefreshCheckbox = document.getElementById('autoRefresh');
    if (autoRefreshCheckbox) {
        autoRefreshCheckbox.addEventListener('change', (e) => {
            if (e.target.checked) {
                startAutoRefresh();
            } else {
                stopAutoRefresh();
            }
        });
    }

    // Modal close
    const bookingModal = document.getElementById('bookingModal');
    if (bookingModal) {
        bookingModal.addEventListener('click', (e) => {
            if (e.target.id === 'bookingModal') {
                closeBookingModal();
            }
        });
    }
}

// Filter delivery list
function filterDeliveryList(searchTerm) {
    const lowerCaseSearchTerm = searchTerm.toLowerCase();
    document.querySelectorAll('.delivery-item').forEach(item => {
        const itemText = item.textContent.toLowerCase();
        item.style.display = itemText.includes(lowerCaseSearchTerm) ? '' : 'none';
    });
}

// Focus on specific delivery
function focusOnDelivery(bookingId) {
    const delivery = deliveriesData.find(d => d.id === bookingId);
    if (delivery) {
        const bounds = new google.maps.LatLngBounds();
        bounds.extend({ lat: delivery.pickup.latitude, lng: delivery.pickup.longitude });
        bounds.extend({ lat: delivery.delivery.latitude, lng: delivery.delivery.longitude });
        liveMap.fitBounds(bounds);

        // Highlight the list item
        document.querySelectorAll('.delivery-item').forEach(item => {
            item.classList.remove('bg-orange-100', 'ring-2', 'ring-orange-300');
            if (parseInt(item.dataset.bookingId) === bookingId) {
                item.classList.add('bg-orange-100', 'ring-2', 'ring-orange-300');
                item.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        });
    }
}

// View booking details in modal
async function viewBookingDetails(bookingId) {
    const modal = document.getElementById('bookingModal');
    const modalContent = document.getElementById('modalContent');
    const modalTitle = document.getElementById('modalTitle');

    modal.classList.remove('hidden');
    modalContent.innerHTML = `<div class="text-center py-8"><i class="fas fa-spinner fa-spin text-3xl text-orange-500"></i></div>`;

    try {
        const response = await fetch(`{{ route('api.live-map.booking', '') }}/${bookingId}`, {
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json'
            }
        });

        if (!response.ok) throw new Error('Failed to fetch booking details');

        const data = await response.json();
        modalTitle.textContent = `Details for Booking #${data.booking_id}`;
        modalContent.innerHTML = buildModalContent(data);

    } catch (error) {
        console.error('Error fetching booking details:', error);
        modalContent.innerHTML = `<p class="text-red-500 text-center">Failed to load booking details</p>`;
    }
}

// Close booking modal
function closeBookingModal() {
    const modal = document.getElementById('bookingModal');
    if (modal) {
        modal.classList.add('hidden');
    }
}

// Clear all map elements
function clearMapElements() {
    // Clear direction renderers
    directionsRenderers.forEach(renderer => {
        renderer.setMap(null);
    });
    directionsRenderers = [];

    // Clear markers
    markers.forEach(marker => {
        marker.setMap(null);
    });
    markers = [];

    // Close info windows
    closeAllInfoWindows();
}

// Get status color for routes
function getStatusColor(status) {
    const colors = {
        'pending': '#9CA3AF',
        'confirmed': '#3B82F6',
        'in_progress': '#8B5CF6',
        'delivery_enroute': '#F59E0B',
        'completed': '#10B981',
        'cancelled': '#EF4444'
    };
    return colors[status] || colors['pending'];
}

// Build modal content for booking details
function buildModalContent(data) {
    return `
        <div class="space-y-4">
            <div>
                <h4 class="font-semibold text-gray-500 text-sm">Pickup</h4>
                <p class="text-gray-800">${data.pickup.address}</p>
                <p class="text-gray-600 text-sm">${data.pickup.person_name || 'N/A'} ${data.pickup.person_phone ? '(' + data.pickup.person_phone + ')' : ''}</p>
            </div>
            <div>
                <h4 class="font-semibold text-gray-500 text-sm">Delivery</h4>
                <p class="text-gray-800">${data.delivery.address}</p>
                <p class="text-gray-600 text-sm">${data.delivery.receiver_name || 'N/A'} ${data.delivery.receiver_phone ? '(' + data.delivery.receiver_phone + ')' : ''}</p>
            </div>
            <div class="grid grid-cols-2 gap-4 text-center pt-4 border-t">
                <div>
                    <p class="text-xs text-gray-500">Status</p>
                    <p class="font-medium text-lg capitalize">${data.status.replace('_', ' ')}</p>
                </div>
                <div>
                    <p class="text-xs text-gray-500">Booking ID</p>
                    <p class="font-medium text-lg">${data.booking_id}</p>
                </div>
            </div>
        </div>
    `;
}

// Update map status indicator
function updateMapStatus(status, message) {
    const statusEl = document.getElementById('map-status');
    if (!statusEl) return;

    const icons = {
        loading: 'fas fa-spinner fa-spin',
        ready: 'fas fa-check-circle',
        error: 'fas fa-exclamation-triangle'
    };
    const colors = {
        loading: 'text-blue-500',
        ready: 'text-green-500',
        error: 'text-red-500'
    };

    statusEl.innerHTML = `<i class="${icons[status]} mr-2"></i><span class="text-gray-600">${message}</span>`;
    statusEl.className = `flex items-center text-xs font-medium ${colors[status]}`;
}

// Hide map loading overlay
function hideMapLoading() {
    const loadingEl = document.getElementById('map-loading');
    if (loadingEl) {
        loadingEl.style.display = 'none';
    }
}

// Close all info windows
function closeAllInfoWindows() {
    infoWindows.forEach(iw => iw.close());
    infoWindows = [];
}

// Refresh map data
function refreshMap() {
    loadDeliveryData();
}

// Start auto-refresh
function startAutoRefresh() {
    stopAutoRefresh();
    autoRefreshTimer = setInterval(() => {
        console.log('🔄 Auto-refreshing delivery data...');
        loadDeliveryData();
    }, 30000); // Refresh every 30 seconds
    console.log('✅ Auto-refresh started (30s interval)');
}

// Stop auto-refresh
function stopAutoRefresh() {
    if (autoRefreshTimer) {
        clearInterval(autoRefreshTimer);
        autoRefreshTimer = null;
        console.log('🛑 Auto-refresh stopped');
    }
}

// Fallback initialization if Google Maps fails to load
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        if (typeof google === 'undefined' || !google.maps) {
            console.warn('Google Maps API failed to load');
            updateMapStatus('error', 'Failed to load Google Maps');
        }
    }, 10000);
});

</script>
@endpush
