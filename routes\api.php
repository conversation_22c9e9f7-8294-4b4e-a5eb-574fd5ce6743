<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make sure to check it out!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Live Map API Routes
Route::middleware(['auth:sanctum'])->group(function () {
    Route::get('/live-map/deliveries', [App\Http\Controllers\LiveMapController::class, 'getActiveDeliveries']);
    Route::get('/live-map/booking/{booking}', [App\Http\Controllers\LiveMapController::class, 'getBookingTrackingData']);
});

// Google Maps API Routes
Route::middleware(['auth:sanctum'])->group(function () {
    Route::post('/maps/place-predictions', [App\Http\Controllers\BookingController::class, 'getPlacePredictions']);
    Route::post('/maps/place-details', [App\Http\Controllers\BookingController::class, 'getPlaceDetails']);
    Route::post('/maps/reverse-geocode', [App\Http\Controllers\BookingController::class, 'reverseGeocode']);
});

// Booking Status API Routes (for tracking page)
Route::get('/booking/{booking}/status', function (App\Models\Booking $booking) {
    return response()->json([
        'id' => $booking->id,
        'booking_id' => $booking->booking_id,
        'status' => $booking->status,
        'updated_at' => $booking->updated_at->toISOString(),

    ]);
});



// Additional API routes can be added here for mobile app integration
// or AJAX calls from the frontend
