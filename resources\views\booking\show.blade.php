@extends('layouts.app')

@section('title', 'Booking Details - ' . $booking->booking_id)

@section('content')
<div class="min-h-screen bg-gray-50">
    
    <!-- Header -->
    <div class="bg-white shadow-sm">
        <div class="container mx-auto px-6 py-8">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Booking Details</h1>
                    <p class="text-gray-600 mt-1">{{ $booking->booking_id }}</p>
                </div>
                <div class="mt-4 md:mt-0 flex space-x-4">
                    <a href="{{ route('booking.history') }}" 
                       class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-semibold">
                        <i class="fas fa-arrow-left mr-2"></i>Back to History
                    </a>
                    @if(in_array($booking->status, ['confirmed', 'in_progress']))
                        <a href="{{ route('tracking') }}?booking_id={{ $booking->booking_id }}" 
                           class="brand-orange text-white px-6 py-3 rounded-lg hover:bg-orange-600 transition-colors font-semibold">
                            <i class="fas fa-map-marker-alt mr-2"></i>Live Tracking
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-6 py-8">
        <div class="max-w-6xl mx-auto">
            
            <!-- Status Banner -->
            <div class="mb-8 p-6 rounded-xl
                @switch($booking->status)
                    @case('pending')
                        bg-yellow-50 border border-yellow-200
                        @break
                    @case('confirmed')
                        bg-blue-50 border border-blue-200
                        @break
                    @case('in_progress')
                        bg-purple-50 border border-purple-200
                        @break
                    @case('completed')
                        bg-green-50 border border-green-200
                        @break
                    @case('cancelled')
                        bg-red-50 border border-red-200
                        @break
                    @default
                        bg-gray-50 border border-gray-200
                @endswitch
            ">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full
                            @switch($booking->status)
                                @case('pending')
                                    bg-yellow-100
                                    @break
                                @case('confirmed')
                                    bg-blue-100
                                    @break
                                @case('in_progress')
                                    bg-purple-100
                                    @break
                                @case('completed')
                                    bg-green-100
                                    @break
                                @case('cancelled')
                                    bg-red-100
                                    @break
                                @default
                                    bg-gray-100
                            @endswitch
                        ">
                            <i class="fas 
                                @switch($booking->status)
                                    @case('pending')
                                        fa-clock text-yellow-600
                                        @break
                                    @case('confirmed')
                                        fa-check text-blue-600
                                        @break
                                    @case('in_progress')
                                        fa-truck text-purple-600
                                        @break
                                    @case('completed')
                                        fa-check-circle text-green-600
                                        @break
                                    @case('cancelled')
                                        fa-times-circle text-red-600
                                        @break
                                    @default
                                        fa-box text-gray-600
                                @endswitch
                                text-xl
                            "></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-bold
                                @switch($booking->status)
                                    @case('pending')
                                        text-yellow-900
                                        @break
                                    @case('confirmed')
                                        text-blue-900
                                        @break
                                    @case('in_progress')
                                        text-purple-900
                                        @break
                                    @case('completed')
                                        text-green-900
                                        @break
                                    @case('cancelled')
                                        text-red-900
                                        @break
                                    @default
                                        text-gray-900
                                @endswitch
                            ">
                                {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                            </h3>
                            <p class="text-sm
                                @switch($booking->status)
                                    @case('pending')
                                        text-yellow-700
                                        @break
                                    @case('confirmed')
                                        text-blue-700
                                        @break
                                    @case('in_progress')
                                        text-purple-700
                                        @break
                                    @case('completed')
                                        text-green-700
                                        @break
                                    @case('cancelled')
                                        text-red-700
                                        @break
                                    @default
                                        text-gray-700
                                @endswitch
                            ">
                                @switch($booking->status)
                                    @case('pending')
                                        Your booking is waiting for confirmation
                                        @break
                                    @case('confirmed')
                                        Your booking has been confirmed and is being processed
                                        @break
                                    @case('in_progress')
                                        Your package is on the way
                                        @break
                                    @case('completed')
                                        Your package has been delivered successfully
                                        @break
                                    @case('cancelled')
                                        This booking has been cancelled
                                        @break
                                    @default
                                        Booking status: {{ $booking->status }}
                                @endswitch
                            </p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-600">Created</p>
                        <p class="font-medium text-gray-900">{{ $booking->created_at->format('M d, Y') }}</p>
                        <p class="text-xs text-gray-500">{{ $booking->created_at->format('h:i A') }}</p>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                
                <!-- Main Content -->
                <div class="lg:col-span-2 space-y-8">
                    
                    <!-- Distance & Cost Preview -->
                    <div id="route-preview" class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="bg-blue-100 rounded-full p-3">
                                    <i class="fas fa-route text-blue-600"></i>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Estimated Distance</p>
                                    <p class="text-lg font-semibold text-gray-900" id="estimated-distance">{{ $booking->distance ?? '-- km' }}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-sm text-gray-600">Estimated Duration</p>
                                <p class="text-lg font-semibold text-gray-900" id="estimated-duration">{{ $booking->duration ?? '-- mins' }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Map Preview -->
                    <div id="map-preview" class="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                        <!-- Map Header -->
                        <div class="p-4 bg-gradient-to-r from-orange-50 to-orange-100 border-b border-orange-200">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-route text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-bold text-gray-900">Route Overview</h4>
                                        <p class="text-sm text-gray-600">{{ $booking->pickup_address }} → {{ $booking->delivery_address }}</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button type="button" id="map-view-toggle" class="px-3 py-1.5 text-xs font-medium text-orange-600 bg-white border border-orange-200 rounded-lg hover:bg-orange-50 transition-colors">
                                        <i class="fas fa-layer-group mr-1"></i>
                                        <span id="map-view-text">Satellite</span>
                                    </button>
                                    <button type="button" id="toggle-map" class="px-3 py-1.5 text-xs font-medium text-orange-600 bg-white border border-orange-200 rounded-lg hover:bg-orange-50 transition-colors">
                                        <i class="fas fa-expand-arrows-alt mr-1"></i>
                                        <span id="toggle-text">Expand</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Map Container -->
                        <div class="relative">
                            <div id="booking-map" style="height: 300px; width: 100%;" class="transition-all duration-300"></div>

                            <!-- Map Loading Overlay -->
                            <div id="map-loading" class="absolute inset-0 bg-gray-100 flex items-center justify-center">
                                <div class="text-center">
                                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-2"></div>
                                    <p class="text-sm text-gray-600">Loading route...</p>
                                </div>
                            </div>

                            <!-- Map Controls Overlay -->
                            <div class="absolute top-4 left-4 bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
                                <button type="button" id="center-route" class="map-control-btn block w-full px-3 py-2 text-xs font-medium text-gray-700 hover:bg-gray-50 border-b border-gray-200">
                                    <i class="fas fa-crosshairs mr-2"></i>Center Route
                                </button>
                                <button type="button" id="show-traffic" class="map-control-btn block w-full px-3 py-2 text-xs font-medium text-gray-700 hover:bg-gray-50 border-b border-gray-200">
                                    <i class="fas fa-traffic-light mr-2"></i>
                                    <span id="traffic-text">Show Traffic</span>
                                </button>
                                <button type="button" id="fullscreen-map" class="map-control-btn block w-full px-3 py-2 text-xs font-medium text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-expand mr-2"></i>
                                    <span id="fullscreen-text">Fullscreen</span>
                                </button>
                            </div>

                            <!-- Route Info Overlay -->
                            <div class="absolute bottom-4 left-4 right-4">
                                <div class="bg-white/95 backdrop-blur-sm rounded-lg shadow-lg border border-gray-200 p-3">
                                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3 text-center">
                                        <div>
                                            <div class="text-lg font-bold text-gray-900" id="route-distance">{{ $booking->distance_km ?? '--' }} km</div>
                                            <div class="text-xs text-gray-600">Distance</div>
                                        </div>
                                        <div>
                                            <div class="text-lg font-bold text-gray-900" id="route-duration">{{ $booking->duration ?? '--' }}</div>
                                            <div class="text-xs text-gray-600">Duration</div>
                                        </div>
                                        <div>
                                            <div class="text-lg font-bold text-orange-600">{{ \App\Models\Setting::formatCurrency($booking->final_cost ?? $booking->estimated_cost) }}</div>
                                            <div class="text-xs text-gray-600">Cost</div>
                                        </div>
                                        <div>
                                            <div class="text-lg font-bold text-blue-600" id="route-eta">
                                                @if($booking->status === 'in_progress')
                                                    <span class="animate-pulse">Live</span>
                                                @else
                                                    --
                                                @endif
                                            </div>
                                            <div class="text-xs text-gray-600">ETA</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Live Tracking Map -->
                    @if(in_array($booking->status, ['confirmed', 'in_progress']))
                        <x-live-tracking-card :booking="$booking" height="500px" />
                    @endif
                    
                    <!-- Route Information -->
                    <div class="bg-white rounded-xl shadow-sm">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-bold text-gray-900">Route Information</h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-6">
                                <!-- Pickup -->
                                <div class="flex items-start">
                                    <div class="w-4 h-4 bg-green-500 rounded-full mt-1 mr-4"></div>
                                    <div class="flex-1">
                                        <h4 class="font-medium text-gray-900">Pickup Location</h4>
                                        <p class="text-gray-600 mt-1">{{ $booking->pickup_address }}</p>
                                        <div class="mt-2 text-sm text-gray-500">
                                            <p><strong>Contact:</strong> {{ $booking->pickup_person_name }}</p>
                                            <p><strong>Phone:</strong> {{ $booking->pickup_person_phone }}</p>
                                        </div>
                                        @if($booking->actual_pickup_time)
                                            <p class="text-xs text-green-600 mt-2">
                                                <i class="fas fa-check mr-1"></i>
                                                Picked up at {{ $booking->actual_pickup_time->format('M d, Y h:i A') }}
                                            </p>
                                        @endif
                                    </div>
                                </div>
                                
                                <!-- Route Line -->
                                <div class="flex items-center ml-2">
                                    <div class="w-px h-8 bg-gray-300"></div>
                                </div>
                                
                                <!-- Delivery -->
                                <div class="flex items-start">
                                    <div class="w-4 h-4 bg-red-500 rounded-full mt-1 mr-4"></div>
                                    <div class="flex-1">
                                        <h4 class="font-medium text-gray-900">Delivery Location</h4>
                                        <p class="text-gray-600 mt-1">{{ $booking->delivery_address }}</p>
                                        <div class="mt-2 text-sm text-gray-500">
                                            <p><strong>Receiver:</strong> {{ $booking->receiver_name }}</p>
                                            <p><strong>Phone:</strong> {{ $booking->receiver_phone }}</p>
                                        </div>
                                        @if($booking->delivered_at)
                                            <p class="text-xs text-green-600 mt-2">
                                                <i class="fas fa-check mr-1"></i>
                                                Delivered at {{ $booking->delivered_at->format('M d, Y h:i A') }}
                                            </p>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Package Information -->
                    <div class="bg-white rounded-xl shadow-sm">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-bold text-gray-900">Package Information</h3>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Package Type</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ ucfirst($booking->package_type) }}</dd>
                                </div>
                                @if($booking->package_weight)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Weight</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $booking->package_weight }} kg</dd>
                                    </div>
                                @endif
                                @if($booking->package_description)
                                    <div class="md:col-span-2">
                                        <dt class="text-sm font-medium text-gray-500">Description</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $booking->package_description }}</dd>
                                    </div>
                                @endif
                                @if($booking->special_instructions)
                                    <div class="md:col-span-2">
                                        <dt class="text-sm font-medium text-gray-500">Special Instructions</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $booking->special_instructions }}</dd>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Sidebar -->
                <div class="space-y-6">
                    
                    <!-- Booking Summary -->
                    <div class="bg-white rounded-xl shadow-sm">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-bold text-gray-900">Booking Summary</h3>
                        </div>
                        <div class="p-6 space-y-4">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Booking ID</span>
                                <span class="text-sm font-medium text-gray-900">{{ $booking->booking_id }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Distance</span>
                                <span class="text-sm font-medium text-gray-900">{{ $booking->distance_km ?? 'N/A' }} km</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Estimated Duration</span>
                                <span class="text-sm font-medium text-gray-900">{{ $booking->estimated_duration_minutes ?? 'N/A' }} min</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Payment Method</span>
                                <span class="text-sm font-medium text-gray-900">{{ ucfirst(str_replace('_', ' ', $booking->payment_method)) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Payment Status</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    @switch($booking->payment_status)
                                        @case('paid')
                                            bg-green-100 text-green-800
                                            @break
                                        @case('pending')
                                            bg-yellow-100 text-yellow-800
                                            @break
                                        @case('failed')
                                            bg-red-100 text-red-800
                                            @break
                                        @default
                                            bg-gray-100 text-gray-800
                                    @endswitch
                                ">
                                    {{ ucfirst($booking->payment_status) }}
                                </span>
                            </div>
                            <hr>
                            <div class="flex justify-between items-center">
                                <span class="text-base font-medium text-gray-900">Total Cost</span>
                                <span class="text-lg font-bold text-green-600">{{ \App\Models\Setting::formatCurrency($booking->final_cost ?? $booking->estimated_cost) }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Delivery Status -->
                    <div class="bg-white rounded-xl shadow-sm">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-bold text-gray-900">Delivery Status</h3>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-truck text-blue-600 text-lg"></i>
                                </div>
                                <div class="ml-4">
                                    <h4 class="font-medium text-gray-900">{{ ucfirst(str_replace('_', ' ', $booking->status)) }}</h4>
                                    <p class="text-sm text-gray-600">Booking #{{ $booking->booking_id }}</p>
                                </div>
                            </div>

                            <div class="space-y-2">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">Status</span>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <span class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-1"></span>
                                        {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                    </span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">Created</span>
                                    <span class="text-sm text-gray-900">{{ $booking->created_at->diffForHumans() }}</span>
                                </div>
                                @if($booking->estimated_duration_minutes)
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-600">Estimated Duration</span>
                                        <span class="text-sm text-gray-900">{{ $booking->estimated_duration_minutes }} minutes</span>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <!-- Actions -->
                    <div class="bg-white rounded-xl shadow-sm">
                        <div class="p-6">
                            <div class="space-y-3">
                                @if(in_array($booking->status, ['confirmed', 'in_progress']))
                                    <a href="{{ route('tracking') }}?booking_id={{ $booking->booking_id }}" 
                                       class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white brand-orange hover:bg-orange-600">
                                        <i class="fas fa-map-marker-alt mr-2"></i>Live Tracking
                                    </a>
                                @endif
                                
                                @if($booking->status === 'completed' && !$booking->review)
                                    <button onclick="openReviewModal()" 
                                            class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                        <i class="fas fa-star mr-2"></i>Leave Review
                                    </button>
                                @endif
                                
                                @if($booking->status === 'pending')
                                    <button onclick="cancelBooking()" 
                                            class="w-full inline-flex justify-center items-center px-4 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50">
                                        <i class="fas fa-times mr-2"></i>Cancel Booking
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Map Styles -->
<style>
    /* Custom map styling */
    #booking-map {
        border-radius: 0;
        transition: height 0.3s ease-in-out;
    }

    /* Custom info window styling */
    .gm-style .gm-style-iw-c {
        border-radius: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .gm-style .gm-style-iw-t::after {
        background: white;
    }

    /* Map control buttons */
    .map-control-btn {
        transition: all 0.2s ease;
    }

    .map-control-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* Loading animation */
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }

    .map-loading {
        animation: pulse 2s infinite;
    }

    /* Route info overlay */
    .route-info-overlay {
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    /* Custom scrollbar for info windows */
    .gm-style .gm-style-iw-c::-webkit-scrollbar {
        width: 4px;
    }

    .gm-style .gm-style-iw-c::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
    }

    .gm-style .gm-style-iw-c::-webkit-scrollbar-thumb {
        background: #F97316;
        border-radius: 2px;
    }

    /* Fullscreen mode */
    .fullscreen-map {
        transition: all 0.3s ease-in-out;
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .route-info-overlay {
            bottom: 8px;
            left: 8px;
            right: 8px;
        }

        .route-info-overlay .grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }

        .map-controls {
            top: 8px;
            left: 8px;
        }

        .map-control-btn {
            padding: 8px 12px;
            font-size: 11px;
        }

        #booking-map {
            min-height: 250px;
        }
    }

    /* Enhanced marker animations */
    @keyframes markerBounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-10px);
        }
        60% {
            transform: translateY(-5px);
        }
    }

    .marker-bounce {
        animation: markerBounce 2s infinite;
    }

    /* Status indicator */
    .status-indicator {
        position: relative;
    }

    .status-indicator::before {
        content: '';
        position: absolute;
        top: 50%;
        left: -12px;
        transform: translateY(-50%);
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: currentColor;
    }

    .status-confirmed::before {
        background: #3B82F6;
        animation: pulse 2s infinite;
    }

    .status-in-progress::before {
        background: #8B5CF6;
        animation: pulse 1s infinite;
    }

    .status-completed::before {
        background: #10B981;
    }
</style>

<!-- Google Maps API -->
<script async defer src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google_maps.api_key') }}&libraries=places&callback=initGoogleMaps"></script>

<script>
// Map variables
let map;
let directionsService;
let directionsRenderer;
let pickupMarker;
let deliveryMarker;

// Initialize Google Maps when API is loaded
function initGoogleMaps() {
    console.log('Google Maps API loaded');
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeBookingDetailsMap);
    } else {
        initializeBookingDetailsMap();
    }
}

function initializeBookingDetailsMap() {
    if (typeof google === 'undefined' || !google.maps) {
        console.warn('Google Maps API not loaded yet');
        return;
    }

    // Initialize map with enhanced styling
    const mapElement = document.getElementById('booking-map');
    if (mapElement) {
        map = new google.maps.Map(mapElement, {
            zoom: 12,
            center: { lat: 5.6037, lng: -0.1870 }, // Accra, Ghana
            mapTypeId: google.maps.MapTypeId.ROADMAP,
            styles: [
                {
                    featureType: 'poi',
                    elementType: 'labels',
                    stylers: [{ visibility: 'off' }]
                },
                {
                    featureType: 'transit',
                    elementType: 'labels',
                    stylers: [{ visibility: 'off' }]
                },
                {
                    featureType: 'road',
                    elementType: 'labels.icon',
                    stylers: [{ visibility: 'off' }]
                }
            ],
            disableDefaultUI: true,
            zoomControl: true,
            zoomControlOptions: {
                position: google.maps.ControlPosition.RIGHT_BOTTOM
            },
            fullscreenControl: false,
            streetViewControl: false,
            mapTypeControl: false
        });

        // Initialize traffic layer
        window.trafficLayer = new google.maps.TrafficLayer();

        directionsService = new google.maps.DirectionsService();
        directionsRenderer = new google.maps.DirectionsRenderer({
            suppressMarkers: true,
            polylineOptions: {
                strokeColor: '#F97316',
                strokeWeight: 5,
                strokeOpacity: 0.8
            }
        });
        directionsRenderer.setMap(map);

        // Load booking route
        updateMapAndRoute();

        // Initialize map controls
        initializeMapControls();

        // Add interaction handlers
        addMapInteractionHandlers();
    }

    // Initialize map controls
    initializeMapControls();
}

function initializeMapControls() {
    // Map toggle functionality
    document.getElementById('toggle-map').addEventListener('click', function() {
        const mapContainer = document.getElementById('booking-map');
        const toggleText = document.getElementById('toggle-text');
        const icon = this.querySelector('i');

        if (mapContainer.style.height === '300px' || mapContainer.style.height === '') {
            mapContainer.style.height = '500px';
            toggleText.textContent = 'Collapse';
            icon.className = 'fas fa-compress-arrows-alt mr-1';
        } else {
            mapContainer.style.height = '300px';
            toggleText.textContent = 'Expand';
            icon.className = 'fas fa-expand-arrows-alt mr-1';
        }

        // Trigger map resize
        setTimeout(() => {
            if (map) {
                google.maps.event.trigger(map, 'resize');
                centerRouteOnMap();
            }
        }, 300);
    });

    // Map view toggle (Road/Satellite)
    document.getElementById('map-view-toggle').addEventListener('click', function() {
        const viewText = document.getElementById('map-view-text');
        const icon = this.querySelector('i');

        if (map.getMapTypeId() === google.maps.MapTypeId.ROADMAP) {
            map.setMapTypeId(google.maps.MapTypeId.SATELLITE);
            viewText.textContent = 'Road';
            icon.className = 'fas fa-road mr-1';
        } else {
            map.setMapTypeId(google.maps.MapTypeId.ROADMAP);
            viewText.textContent = 'Satellite';
            icon.className = 'fas fa-layer-group mr-1';
        }
    });

    // Center route button
    document.getElementById('center-route').addEventListener('click', function() {
        centerRouteOnMap();
    });

    // Traffic toggle
    document.getElementById('show-traffic').addEventListener('click', function() {
        const trafficText = document.getElementById('traffic-text');
        const icon = this.querySelector('i');

        if (window.trafficLayer.getMap()) {
            window.trafficLayer.setMap(null);
            trafficText.textContent = 'Show Traffic';
            icon.className = 'fas fa-traffic-light mr-2';
        } else {
            window.trafficLayer.setMap(map);
            trafficText.textContent = 'Hide Traffic';
            icon.className = 'fas fa-eye-slash mr-2';
        }
    });

    // Fullscreen toggle
    document.getElementById('fullscreen-map').addEventListener('click', function() {
        const mapPreview = document.getElementById('map-preview');
        const mapContainer = document.getElementById('booking-map');
        const fullscreenText = document.getElementById('fullscreen-text');
        const icon = this.querySelector('i');

        if (!mapPreview.classList.contains('fullscreen-map')) {
            // Enter fullscreen
            mapPreview.classList.add('fullscreen-map');
            mapPreview.style.position = 'fixed';
            mapPreview.style.top = '0';
            mapPreview.style.left = '0';
            mapPreview.style.width = '100vw';
            mapPreview.style.height = '100vh';
            mapPreview.style.zIndex = '9999';
            mapContainer.style.height = 'calc(100vh - 120px)';
            fullscreenText.textContent = 'Exit Fullscreen';
            icon.className = 'fas fa-compress mr-2';
            document.body.style.overflow = 'hidden';
        } else {
            // Exit fullscreen
            mapPreview.classList.remove('fullscreen-map');
            mapPreview.style.position = '';
            mapPreview.style.top = '';
            mapPreview.style.left = '';
            mapPreview.style.width = '';
            mapPreview.style.height = '';
            mapPreview.style.zIndex = '';
            mapContainer.style.height = '300px';
            fullscreenText.textContent = 'Fullscreen';
            icon.className = 'fas fa-expand mr-2';
            document.body.style.overflow = '';
        }

        // Trigger map resize
        setTimeout(() => {
            if (map) {
                google.maps.event.trigger(map, 'resize');
                centerRouteOnMap();
            }
        }, 300);
    });

    // ESC key to exit fullscreen
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const mapPreview = document.getElementById('map-preview');
            if (mapPreview.classList.contains('fullscreen-map')) {
                document.getElementById('fullscreen-map').click();
            }
        }
    });
}

function updateMapAndRoute() {
    const pickupLat = {{ $booking->pickup_latitude ?? 5.6037 }};
    const pickupLng = {{ $booking->pickup_longitude ?? -0.1870 }};
    const deliveryLat = {{ $booking->delivery_latitude ?? 5.6037 }};
    const deliveryLng = {{ $booking->delivery_longitude ?? -0.1870 }};

    if (!map || isNaN(pickupLat) || isNaN(pickupLng) || isNaN(deliveryLat) || isNaN(deliveryLng)) {
        // Hide loading overlay even if coordinates are invalid
        const loadingElement = document.getElementById('map-loading');
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
        return;
    }

    // Clear existing markers
    if (pickupMarker) pickupMarker.setMap(null);
    if (deliveryMarker) deliveryMarker.setMap(null);

    // Enhanced pickup marker with custom design
    pickupMarker = new google.maps.Marker({
        position: { lat: pickupLat, lng: pickupLng },
        map: map,
        title: 'Pickup Location',
        icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="20" cy="20" r="18" fill="#10B981" stroke="white" stroke-width="3"/>
                    <circle cx="20" cy="20" r="12" fill="white"/>
                    <path d="M16 20l3 3 6-6" stroke="#10B981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            `),
            scaledSize: new google.maps.Size(40, 40),
            anchor: new google.maps.Point(20, 20)
        },
        animation: google.maps.Animation.DROP
    });

    // Enhanced delivery marker with custom design
    deliveryMarker = new google.maps.Marker({
        position: { lat: deliveryLat, lng: deliveryLng },
        map: map,
        title: 'Delivery Location',
        icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="20" cy="20" r="18" fill="#EF4444" stroke="white" stroke-width="3"/>
                    <circle cx="20" cy="20" r="12" fill="white"/>
                    <path d="M20 14v6l4 4" stroke="#EF4444" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            `),
            scaledSize: new google.maps.Size(40, 40),
            anchor: new google.maps.Point(20, 20)
        },
        animation: google.maps.Animation.DROP
    });

    // Enhanced info windows with better styling
    const pickupInfoWindow = new google.maps.InfoWindow({
        content: `
            <div class="p-4 max-w-xs">
                <div class="flex items-center mb-2">
                    <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                    <h4 class="font-bold text-gray-900">Pickup Location</h4>
                </div>
                <p class="text-sm text-gray-700 mb-2">{{ $booking->pickup_address }}</p>
                <div class="text-xs text-gray-600">
                    <p><strong>Contact:</strong> {{ $booking->sender_name }}</p>
                    <p><strong>Phone:</strong> {{ $booking->sender_phone }}</p>
                    @if($booking->pickup_notes)
                        <p><strong>Notes:</strong> {{ $booking->pickup_notes }}</p>
                    @endif
                </div>
            </div>
        `,
        maxWidth: 300
    });

    const deliveryInfoWindow = new google.maps.InfoWindow({
        content: `
            <div class="p-4 max-w-xs">
                <div class="flex items-center mb-2">
                    <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                    <h4 class="font-bold text-gray-900">Delivery Location</h4>
                </div>
                <p class="text-sm text-gray-700 mb-2">{{ $booking->delivery_address }}</p>
                <div class="text-xs text-gray-600">
                    <p><strong>Contact:</strong> {{ $booking->receiver_name }}</p>
                    <p><strong>Phone:</strong> {{ $booking->receiver_phone }}</p>
                    @if($booking->delivery_notes)
                        <p><strong>Notes:</strong> {{ $booking->delivery_notes }}</p>
                    @endif
                </div>
            </div>
        `,
        maxWidth: 300
    });

    // Add click listeners for info windows
    pickupMarker.addListener('click', () => {
        deliveryInfoWindow.close();
        pickupInfoWindow.open(map, pickupMarker);
        window.currentInfoWindow = pickupInfoWindow;
    });

    deliveryMarker.addListener('click', () => {
        pickupInfoWindow.close();
        deliveryInfoWindow.open(map, deliveryMarker);
        window.currentInfoWindow = deliveryInfoWindow;
    });

    // Add close listeners to track current info window
    pickupInfoWindow.addListener('closeclick', () => {
        window.currentInfoWindow = null;
    });

    deliveryInfoWindow.addListener('closeclick', () => {
        window.currentInfoWindow = null;
    });

    // Calculate and display route with enhanced options
    directionsService.route({
        origin: { lat: pickupLat, lng: pickupLng },
        destination: { lat: deliveryLat, lng: deliveryLng },
        travelMode: google.maps.TravelMode.DRIVING,
        avoidHighways: false,
        avoidTolls: false,
        optimizeWaypoints: true
    }, function(result, status) {
        if (status === 'OK') {
            directionsRenderer.setDirections(result);

            // Update distance and duration in the overlay
            const route = result.routes[0];
            if (route && route.legs && route.legs.length > 0) {
                const leg = route.legs[0];
                const distanceElement = document.getElementById('route-distance');
                const durationElement = document.getElementById('route-duration');

                if (distanceElement) {
                    distanceElement.textContent = leg.distance.text;
                }
                if (durationElement) {
                    durationElement.textContent = leg.duration.text;
                }
            }

            // Center the route on the map
            centerRouteOnMap();

            // Hide loading overlay
            const loadingElement = document.getElementById('map-loading');
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }
        } else {
            console.error('Directions request failed due to ' + status);

            // Show error message in loading overlay
            const loadingElement = document.getElementById('map-loading');
            if (loadingElement) {
                loadingElement.innerHTML = `
                    <div class="text-center">
                        <div class="text-red-500 mb-2">
                            <i class="fas fa-exclamation-triangle text-2xl"></i>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">Unable to load route</p>
                        <button onclick="retryMapLoad()" class="px-4 py-2 bg-orange-500 text-white text-xs rounded-lg hover:bg-orange-600 transition-colors">
                            <i class="fas fa-redo mr-1"></i>Retry
                        </button>
                    </div>
                `;
            }

            // Still show markers even if route fails
            centerRouteOnMap();
        }
    });
}

// Function to center route on map
function centerRouteOnMap() {
    if (pickupMarker && deliveryMarker && map) {
        const bounds = new google.maps.LatLngBounds();
        bounds.extend(pickupMarker.getPosition());
        bounds.extend(deliveryMarker.getPosition());
        map.fitBounds(bounds, { padding: 50 });
    }
}

// Retry function for failed map loads
function retryMapLoad() {
    const loadingElement = document.getElementById('map-loading');
    if (loadingElement) {
        loadingElement.innerHTML = `
            <div class="text-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-2"></div>
                <p class="text-sm text-gray-600">Retrying...</p>
            </div>
        `;
    }

    // Retry loading the route
    setTimeout(() => {
        updateMapAndRoute();
    }, 1000);
}

// Enhanced map interaction handlers
function addMapInteractionHandlers() {
    if (!map) return;

    // Add click handler to close info windows when clicking on map
    map.addListener('click', () => {
        if (window.currentInfoWindow) {
            window.currentInfoWindow.close();
        }
    });

    // Add idle handler to update route info when map stops moving
    map.addListener('idle', () => {
        // Update any dynamic content if needed
        console.log('Map idle - ready for interactions');
    });

    // Add zoom change handler
    map.addListener('zoom_changed', () => {
        const zoom = map.getZoom();
        // Adjust marker sizes based on zoom level
        if (pickupMarker && deliveryMarker) {
            const size = Math.max(30, Math.min(50, zoom * 3));
            const newSize = new google.maps.Size(size, size);

            pickupMarker.setIcon({
                ...pickupMarker.getIcon(),
                scaledSize: newSize,
                anchor: new google.maps.Point(size/2, size/2)
            });

            deliveryMarker.setIcon({
                ...deliveryMarker.getIcon(),
                scaledSize: newSize,
                anchor: new google.maps.Point(size/2, size/2)
            });
        }
    });
}

// Auto-refresh for active bookings
@if(in_array($booking->status, ['confirmed', 'in_progress']))
    setInterval(() => {
        // Refresh tracking component if it exists
        if (typeof refreshTrackingMap{{ $booking->id }} === 'function') {
            refreshTrackingMap{{ $booking->id }}();
        }
    }, 30000); // Refresh every 30 seconds
@endif

function openReviewModal() {
    // Placeholder for review modal
    alert('Review functionality will be implemented soon!');
}

function cancelBooking() {
    if (confirm('Are you sure you want to cancel this booking?')) {
        // Implement cancellation logic
        alert('Cancellation functionality will be implemented soon!');
    }
}
</script>
@endsection
